<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.PidLidPropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.PidLidPropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PidLidPropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PidLidPropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.PidLidPropertyDescriptor" class="title">Uses of Class<br>com.aspose.email.PidLidPropertyDescriptor</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ADDRESS_BOOK_PROVIDER_ARRAY_TYPE">ADDRESS_BOOK_PROVIDER_ARRAY_TYPE</a></strong></code>
<div class="block">
 Specifies the state of the electronic addresses of the contact and represents a set of bit flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ADDRESS_BOOK_PROVIDER_EMAIL_LIST">ADDRESS_BOOK_PROVIDER_EMAIL_LIST</a></strong></code>
<div class="block">
 Specifies which electronic address properties are set on the Contact object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ADDRESS_COUNTRY_CODE">ADDRESS_COUNTRY_CODE</a></strong></code>
<div class="block">
 Specifies the country code portion of the mailing address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AGING_DONT_AGE_ME">AGING_DONT_AGE_ME</a></strong></code>
<div class="block">
 Specifies whether to automatically archive the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ALL_ATTENDEES_STRING">ALL_ATTENDEES_STRING</a></strong></code>
<div class="block">
 Specifies a list of all the attendees except for the organizer, including resources and unsendable attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ALLOW_EXTERNAL_CHECK">ALLOW_EXTERNAL_CHECK</a></strong></code>
<div class="block">
 This property is set to TRUE.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ANNIVERSARY_EVENT_ENTRY_ID">ANNIVERSARY_EVENT_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the Appointment object that represents an anniversary of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_AUXILIARY_FLAGS">APPOINTMENT_AUXILIARY_FLAGS</a></strong></code>
<div class="block">
 Specifies a bit field that describes the auxiliary state of the object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_COLOR">APPOINTMENT_COLOR</a></strong></code>
<div class="block">
 Specifies the color to be used when displaying the Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_COUNTER_PROPOSAL">APPOINTMENT_COUNTER_PROPOSAL</a></strong></code>
<div class="block">
 Indicates whether a Meeting Response object is a counter proposal.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_DURATION">APPOINTMENT_DURATION</a></strong></code>
<div class="block">
 Specifies the length of the event, in minutes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_END_DATE">APPOINTMENT_END_DATE</a></strong></code>
<div class="block">
 Indicates the date that the appointment ends.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_END_TIME">APPOINTMENT_END_TIME</a></strong></code>
<div class="block">
 Indicates the time that the appointment ends.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_END_WHOLE">APPOINTMENT_END_WHOLE</a></strong></code>
<div class="block">
 Specifies the end date and time for the event.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_LAST_SEQUENCE">APPOINTMENT_LAST_SEQUENCE</a></strong></code>
<div class="block">
 Indicates to the organizer the last sequence number that was sent to any attendee.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_MESSAGE_CLASS">APPOINTMENT_MESSAGE_CLASS</a></strong></code>
<div class="block">
 Indicates the message class of the Meeting object to be generated from the Meeting Request object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_NOT_ALLOW_PROPOSE">APPOINTMENT_NOT_ALLOW_PROPOSE</a></strong></code>
<div class="block">
 Indicates whether attendees are not allowed to propose a new date and/or time for the meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_PROPOSAL_NUMBER">APPOINTMENT_PROPOSAL_NUMBER</a></strong></code>
<div class="block">
 Specifies the number of attendees who have sent counter proposals that have not been accepted or rejected by the organizer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_PROPOSED_DURATION">APPOINTMENT_PROPOSED_DURATION</a></strong></code>
<div class="block">
 Indicates the proposed value for the PidLidAppointmentDuration property (section 2.11) for a counter proposal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_PROPOSED_END_WHOLE">APPOINTMENT_PROPOSED_END_WHOLE</a></strong></code>
<div class="block">
 Specifies the proposed value for the PidLidAppointmentEndWhole property (section 2.14) for a counter proposal.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_PROPOSED_START_WHOLE">APPOINTMENT_PROPOSED_START_WHOLE</a></strong></code>
<div class="block">
 Specifies the proposed value for the PidLidAppointmentStartWhole property (section 2.29) for a counter proposal.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_RECUR">APPOINTMENT_RECUR</a></strong></code>
<div class="block">
 Specifies the dates and times when a recurring series occurs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_REPLY_NAME">APPOINTMENT_REPLY_NAME</a></strong></code>
<div class="block">
 Specifies the user who last replied to the meeting request or meeting update.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_REPLY_TIME">APPOINTMENT_REPLY_TIME</a></strong></code>
<div class="block">
 Specifies the date and time at which the attendee responded to a received meeting request or Meeting Update object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_SEQUENCE">APPOINTMENT_SEQUENCE</a></strong></code>
<div class="block">
 Specifies the sequence number of a Meeting object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_SEQUENCE_TIME">APPOINTMENT_SEQUENCE_TIME</a></strong></code>
<div class="block">
 Indicates the date and time at which the PidLidAppointmentSequence property (section 2.25) was last modified.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_START_DATE">APPOINTMENT_START_DATE</a></strong></code>
<div class="block">
 Identifies the date that the appointment starts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_START_TIME">APPOINTMENT_START_TIME</a></strong></code>
<div class="block">
 Identifies the time that the appointment starts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_START_WHOLE">APPOINTMENT_START_WHOLE</a></strong></code>
<div class="block">
 Specifies the start date and time of the appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_STATE_FLAGS">APPOINTMENT_STATE_FLAGS</a></strong></code>
<div class="block">
 Specifies a bit field that describes the state of the object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_SUB_TYPE">APPOINTMENT_SUB_TYPE</a></strong></code>
<div class="block">
 Specifies whether the event is an all-day event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_TIME_ZONE_DEFINITION_END_DISPLAY">APPOINTMENT_TIME_ZONE_DEFINITION_END_DISPLAY</a></strong></code>
<div class="block">
 Specifies time zone information that indicates the time zone of the PidLidAppointmentEndWhole property (section 2.14).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_TIME_ZONE_DEFINITION_RECUR">APPOINTMENT_TIME_ZONE_DEFINITION_RECUR</a></strong></code>
<div class="block">
 Specifies time zone information that describes how to convert the meeting date and time on a recurring series to and from UTC.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_TIME_ZONE_DEFINITION_START_DISPLAY">APPOINTMENT_TIME_ZONE_DEFINITION_START_DISPLAY</a></strong></code>
<div class="block">
 Specifies time zone information that indicates the time zone of the PidLidAppointmentStartWhole property (section 2.29).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_UNSENDABLE_RECIPIENTS">APPOINTMENT_UNSENDABLE_RECIPIENTS</a></strong></code>
<div class="block">
 Contains a list of unsendable attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPOINTMENT_UPDATE_TIME">APPOINTMENT_UPDATE_TIME</a></strong></code>
<div class="block">
 Indicates the time at which the appointment was last updated.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTENDEE_CRITICAL_CHANGE">ATTENDEE_CRITICAL_CHANGE</a></strong></code>
<div class="block">
 Specifies the date and time at which the meeting-related object was sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTO_FILL_LOCATION">AUTO_FILL_LOCATION</a></strong></code>
<div class="block">
 Indicates whether the value of the PidLidLocation property (section 2.159) is set to the PidTagDisplayName property (section 2.667).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTO_LOG">AUTO_LOG</a></strong></code>
<div class="block">
 Specifies to the application whether to create a Journal object for each action associated with this Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTO_PROCESS_STATE">AUTO_PROCESS_STATE</a></strong></code>
<div class="block">
 Specifies the options used in the automatic processing of email messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTO_START_CHECK">AUTO_START_CHECK</a></strong></code>
<div class="block">
 Specifies whether to automatically start the conferencing application when a reminder for the start of a meeting is executed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BILLING">BILLING</a></strong></code>
<div class="block">
 Specifies billing information for the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BIRTHDAY_EVENT_ENTRY_ID">BIRTHDAY_EVENT_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of an optional Appointment object that represents the birthday of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BIRTHDAY_LOCAL">BIRTHDAY_LOCAL</a></strong></code>
<div class="block">
 Specifies the birthday of a contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BUSINESS_CARD_CARD_PICTURE">BUSINESS_CARD_CARD_PICTURE</a></strong></code>
<div class="block">
 Contains the image to be used on a business card.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BUSINESS_CARD_DISPLAY_DEFINITION">BUSINESS_CARD_DISPLAY_DEFINITION</a></strong></code>
<div class="block">
 Contains user customization details for displaying a contact as a business card.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BUSY_STATUS">BUSY_STATUS</a></strong></code>
<div class="block">
 Specifies the availability of a user for the event described by the object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_TYPE">CALENDAR_TYPE</a></strong></code>
<div class="block">
 Contains the value of the CalendarType field from the PidLidAppointmentRecur property (section 2.22).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CATEGORIES">CATEGORIES</a></strong></code>
<div class="block">
 Contains the array of text labels assigned to this Message object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CC_ATTENDEES_STRING">CC_ATTENDEES_STRING</a></strong></code>
<div class="block">
 Contains a list of all the sendable attendees who are also optional attendees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CHANGE_HIGHLIGHT">CHANGE_HIGHLIGHT</a></strong></code>
<div class="block">
 Specifies a bit field that indicates how the Meeting object has changed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLASSIFICATION">CLASSIFICATION</a></strong></code>
<div class="block">
 Contains a list of the classification categories to which the associated Message object has been assigned.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLASSIFICATION_DESCRIPTION">CLASSIFICATION_DESCRIPTION</a></strong></code>
<div class="block">
 Contains a human-readable summary of each of the classification categories included in the PidLidClassification property (section 2.53).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLASSIFICATION_GUID">CLASSIFICATION_GUID</a></strong></code>
<div class="block">
 Contains the GUID that identifies the list of email classification categories used by a Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLASSIFICATION_KEEP">CLASSIFICATION_KEEP</a></strong></code>
<div class="block">
 Indicates whether the message uses any classification categories.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLASSIFIED">CLASSIFIED</a></strong></code>
<div class="block">
 Indicates whether the contents of this message are regarded as classified information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLEAN_GLOBAL_OBJECT_ID">CLEAN_GLOBAL_OBJECT_ID</a></strong></code>
<div class="block">
 Contains the value of the PidLidGlobalObjectId property (section 2.142) for an object all zero.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLIENT_INTENT">CLIENT_INTENT</a></strong></code>
<div class="block">
 Indicates what actions the user has taken on this Meeting object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLIP_END">CLIP_END</a></strong></code>
<div class="block">
 Specifies the end date and time of the event in UTC.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CLIP_START">CLIP_START</a></strong></code>
<div class="block">
 Specifies the start date and time of the event in UTC.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COLLABORATE_DOC">COLLABORATE_DOC</a></strong></code>
<div class="block">
 Specifies the document to be launched when the user joins the meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COMMON_END">COMMON_END</a></strong></code>
<div class="block">
 Indicates the end time for the Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COMMON_START">COMMON_START</a></strong></code>
<div class="block">
 Indicates the start time for the Message object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COMPANIES">COMPANIES</a></strong></code>
<div class="block">
 Contains a list of company names, each of which is associated with a contact that is specified in the PidLidContacts property ([MS-OXCMSG] section ********.2).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONFERENCING_CHECK">CONFERENCING_CHECK</a></strong></code>
<div class="block">
 Area: Conferencing
 Canonical name: PidLidConferencingCheck
 Alternate names: dispidConfCheck</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONFERENCING_TYPE">CONFERENCING_TYPE</a></strong></code>
<div class="block">
 Specifies the type of the meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_CHARACTER_SET">CONTACT_CHARACTER_SET</a></strong></code>
<div class="block">
 Specifies the character set used for a Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_ITEM_DATA">CONTACT_ITEM_DATA</a></strong></code>
<div class="block">
 Specifies the visible fields in the application's user interface that are used to help display the contact information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_ENTRY">CONTACT_LINK_ENTRY</a></strong></code>
<div class="block">
 Contains the elements of the PidLidContacts property (section 2.77).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_GLOBAL_ADDRESS_LIST_LINK_ID">CONTACT_LINK_GLOBAL_ADDRESS_LIST_LINK_ID</a></strong></code>
<div class="block">
 Specifies the GUID of the GAL contact to which the duplicate contact is linked.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_GLOBAL_ADDRESS_LIST_LINK_STATE">CONTACT_LINK_GLOBAL_ADDRESS_LIST_LINK_STATE</a></strong></code>
<div class="block">
 Specifies the state of the linking between the GAL contact and the duplicate contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_LINK_REJECT_HISTORY">CONTACT_LINK_LINK_REJECT_HISTORY</a></strong></code>
<div class="block">
 Contains a list of GAL contacts that were previously rejected for linking with the duplicate contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_NAME">CONTACT_LINK_NAME</a></strong></code>
<div class="block">
 Area: Contact Properties
 Canonical name: PidLidContactLinkName
 Alternate names: dispidContactLinkName</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_SEARCH_KEY">CONTACT_LINK_SEARCH_KEY</a></strong></code>
<div class="block">
 Contains the list of SearchKeys for a Contact object linked to by the Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINK_SMTP_ADDRESS_CACHE">CONTACT_LINK_SMTP_ADDRESS_CACHE</a></strong></code>
<div class="block">
 Contains a list of the SMTP addresses that are used by the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_LINKED_GLOBAL_ADDRESS_LIST_ENTRY_ID">CONTACT_LINKED_GLOBAL_ADDRESS_LIST_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the GAL contact to which the duplicate contact is linked.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_USER_FIELD_1">CONTACT_USER_FIELD_1</a></strong></code>
<div class="block">
 Contains text used to add custom text to a business card representation of a Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_USER_FIELD_2">CONTACT_USER_FIELD_2</a></strong></code>
<div class="block">
 Contains text used to add custom text to a business card representation of a Contact object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_USER_FIELD_3">CONTACT_USER_FIELD_3</a></strong></code>
<div class="block">
 Contains text used to add custom text to a business card representation of a Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACT_USER_FIELD_4">CONTACT_USER_FIELD_4</a></strong></code>
<div class="block">
 Contains text used to add custom text to a business card representation of a Contact object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTACTS">CONTACTS</a></strong></code>
<div class="block">
 Contains the PidTagDisplayName property (section 2.667) of each Address Book EntryID referenced in the value of the PidLidContactLinkEntry property (section 2.70).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_ACTION_LAST_APPLIED_TIME">CONVERSATION_ACTION_LAST_APPLIED_TIME</a></strong></code>
<div class="block">
 Contains the time, in UTC, that an Email object was last received in the conversation, or the last time that the user modified the conversation action, whichever occurs later.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_ACTION_MAX_DELIVERY_TIME">CONVERSATION_ACTION_MAX_DELIVERY_TIME</a></strong></code>
<div class="block">
 Contains the maximum value of the PidTagMessageDeliveryTime property (section conversation action on the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_ACTION_MOVE_FOLDER_EID">CONVERSATION_ACTION_MOVE_FOLDER_EID</a></strong></code>
<div class="block">
 Contains the EntryID for the destination folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_ACTION_MOVE_STORE_EID">CONVERSATION_ACTION_MOVE_STORE_EID</a></strong></code>
<div class="block">
 Contains the EntryID for a move to a folder in a different message store.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_ACTION_VERSION">CONVERSATION_ACTION_VERSION</a></strong></code>
<div class="block">
 Contains the version of the conversation action FAI message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONVERSATION_PROCESSED">CONVERSATION_PROCESSED</a></strong></code>
<div class="block">
 Specifies a sequential number to be used in the processing of a conversation action.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CURRENT_VERSION">CURRENT_VERSION</a></strong></code>
<div class="block">
 Specifies the build number of the client application that sent the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CURRENT_VERSION_NAME">CURRENT_VERSION_NAME</a></strong></code>
<div class="block">
 Specifies the name of the client application that sent the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAY_INTERVAL">DAY_INTERVAL</a></strong></code>
<div class="block">
 Identifies the day interval for the recurrence pattern.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAY_OF_MONTH">DAY_OF_MONTH</a></strong></code>
<div class="block">
 Identifies the day of the month for the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DELEGATE_MAIL">DELEGATE_MAIL</a></strong></code>
<div class="block">
 Indicates whether a delegate responded to the meeting request.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DEPARTMENT">DEPARTMENT</a></strong></code>
<div class="block">
 This property is ignored by the server and is set to an empty string by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DIRECTORY">DIRECTORY</a></strong></code>
<div class="block">
 Specifies the directory server to be used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DISTRIBUTION_LIST_CHECKSUM">DISTRIBUTION_LIST_CHECKSUM</a></strong></code>
<div class="block">
 Specifies the 32-bit cyclic redundancy check (CRC) polynomial checksum, as property (section 2.96).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DISTRIBUTION_LIST_MEMBERS">DISTRIBUTION_LIST_MEMBERS</a></strong></code>
<div class="block">
 Specifies the list of EntryIDs of the objects corresponding to the members of the personal distribution list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DISTRIBUTION_LIST_NAME">DISTRIBUTION_LIST_NAME</a></strong></code>
<div class="block">
 Specifies the name of the personal distribution list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DISTRIBUTION_LIST_ONE_OFF_MEMBERS">DISTRIBUTION_LIST_ONE_OFF_MEMBERS</a></strong></code>
<div class="block">
 Specifies the list of one-off EntryIDs corresponding to the members of the personal distribution list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DISTRIBUTION_LIST_STREAM">DISTRIBUTION_LIST_STREAM</a></strong></code>
<div class="block">
 Specifies the list of EntryIDs and one-off EntryIDs corresponding to the members of the personal distribution list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_1_ADDRESS_TYPE">EMAIL_1_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Specifies the address type of an electronic address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_1_DISPLAY_NAME">EMAIL_1_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the user-readable display name for the email address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_1_EMAIL_ADDRESS">EMAIL_1_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Specifies the email address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_1_ORIGINAL_DISPLAY_NAME">EMAIL_1_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the SMTP email address that corresponds to the email address for the Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_1_ORIGINAL_ENTRY_ID">EMAIL_1_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the object corresponding to this electronic address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_2_ADDRESS_TYPE">EMAIL_2_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Specifies the address type of the electronic address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_2_DISPLAY_NAME">EMAIL_2_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the user-readable display name for the email address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_2_EMAIL_ADDRESS">EMAIL_2_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Specifies the email address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_2_ORIGINAL_DISPLAY_NAME">EMAIL_2_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the SMTP email address that corresponds to the email address for the Contact object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_2_ORIGINAL_ENTRY_ID">EMAIL_2_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the object that corresponds to this electronic address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_3_ADDRESS_TYPE">EMAIL_3_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Specifies the address type of the electronic address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_3_DISPLAY_NAME">EMAIL_3_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the user-readable display name for the email address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_3_EMAIL_ADDRESS">EMAIL_3_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Specifies the email address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_3_ORIGINAL_DISPLAY_NAME">EMAIL_3_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Specifies the SMTP email address that corresponds to the email address for the Contact object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EMAIL_3_ORIGINAL_ENTRY_ID">EMAIL_3_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the object that corresponds to this electronic address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#END_RECURRENCE_DATE">END_RECURRENCE_DATE</a></strong></code>
<div class="block">
 Identifies the end date of the recurrence range.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#END_RECURRENCE_TIME">END_RECURRENCE_TIME</a></strong></code>
<div class="block">
 Identifies the end time of the recurrence range.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCEPTION_REPLACE_TIME">EXCEPTION_REPLACE_TIME</a></strong></code>
<div class="block">
 Specifies the date and time, in UTC, within a recurrence pattern that an exception will replace.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#F_EXCEPTIONAL_ATTENDEES">F_EXCEPTIONAL_ATTENDEES</a></strong></code>
<div class="block">
 Indicates that the object is a Recurring Calendar object with one or more exceptions, structure, as described in [MS-OXCDATA] section 2.8.3.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#F_EXCEPTIONAL_BODY">F_EXCEPTIONAL_BODY</a></strong></code>
<div class="block">
 Indicates that the Exception Embedded Message object has a body that differs from the Recurring Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#F_INVITED">F_INVITED</a></strong></code>
<div class="block">
 Indicates whether invitations have been sent for the meeting that this Meeting object represents.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#F_OTHERS_APPOINTMENT">F_OTHERS_APPOINTMENT</a></strong></code>
<div class="block">
 Indicates whether the Calendar folder from which the meeting was opened is another user's calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_1_ADDRESS_TYPE">FAX_1_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Contains the string value "FAX".</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_1_EMAIL_ADDRESS">FAX_1_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Contains a user-readable display name, followed by the "@" character, followed by a fax number.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_1_ORIGINAL_DISPLAY_NAME">FAX_1_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Contains the same value as the PidTagNormalizedSubject property (section 2.803).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_1_ORIGINAL_ENTRY_ID">FAX_1_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies a one-off EntryID that corresponds to this fax address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_2_ADDRESS_TYPE">FAX_2_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Contains the string value "FAX".</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_2_EMAIL_ADDRESS">FAX_2_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Contains a user-readable display name, followed by the "@" character, followed by a fax number.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_2_ORIGINAL_DISPLAY_NAME">FAX_2_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Contains the same value as the PidTagNormalizedSubject property (section 2.803).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_2_ORIGINAL_ENTRY_ID">FAX_2_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies a one-off EntryID corresponding to this fax address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_3_ADDRESS_TYPE">FAX_3_ADDRESS_TYPE</a></strong></code>
<div class="block">
 Contains the string value "FAX".</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_3_EMAIL_ADDRESS">FAX_3_EMAIL_ADDRESS</a></strong></code>
<div class="block">
 Contains a user-readable display name, followed by the "@" character, followed by a fax number.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_3_ORIGINAL_DISPLAY_NAME">FAX_3_ORIGINAL_DISPLAY_NAME</a></strong></code>
<div class="block">
 Contains the same value as the PidTagNormalizedSubject property (section 2.803).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FAX_3_ORIGINAL_ENTRY_ID">FAX_3_ORIGINAL_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies a one-off EntryID that corresponds to this fax address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FILE_UNDER">FILE_UNDER</a></strong></code>
<div class="block">
 Specifies the name under which to file a contact when displaying a list of contacts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FILE_UNDER_ID">FILE_UNDER_ID</a></strong></code>
<div class="block">
 Specifies how to generate and recompute the value of the PidLidFileUnder property (section 2.132) when other contact name properties change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FILE_UNDER_LIST">FILE_UNDER_LIST</a></strong></code>
<div class="block">
 Specifies a list of possible values for the PidLidFileUnderId property (section 2.133).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FLAG_REQUEST">FLAG_REQUEST</a></strong></code>
<div class="block">
 Contains user-specifiable text to be associated with the flag.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FLAG_STRING">FLAG_STRING</a></strong></code>
<div class="block">
 Contains an index identifying one of a set of pre-defined text strings to be associated with the flag.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FORWARD_INSTANCE">FORWARD_INSTANCE</a></strong></code>
<div class="block">
 Indicates whether the Meeting Request object represents an exception to a recurring invitation sent by the organizer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FORWARD_NOTIFICATION_RECIPIENTS">FORWARD_NOTIFICATION_RECIPIENTS</a></strong></code>
<div class="block">
 Contains a list of RecipientRow structures, as described in [MS-OXCDATA] section 2.8.3, that indicate the recipients of a meeting forward.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FREE_BUSY_LOCATION">FREE_BUSY_LOCATION</a></strong></code>
<div class="block">
 Specifies a URL path from which a client can retrieve free/busy status information for the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#GLOBAL_OBJECT_ID">GLOBAL_OBJECT_ID</a></strong></code>
<div class="block">
 Contains an ID for an object that represents an exception to a recurring series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HAS_PICTURE">HAS_PICTURE</a></strong></code>
<div class="block">
 Specifies whether the attachment has a picture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HOME_ADDRESS">HOME_ADDRESS</a></strong></code>
<div class="block">
 Specifies the complete address of the home address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HOME_ADDRESS_COUNTRY_CODE">HOME_ADDRESS_COUNTRY_CODE</a></strong></code>
<div class="block">
 Specifies the country code portion of the home address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HTML">HTML</a></strong></code>
<div class="block">
 Specifies the business webpage URL of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#I_CALENDAR_DAY_OF_WEEK_MASK">I_CALENDAR_DAY_OF_WEEK_MASK</a></strong></code>
<div class="block">
 Identifies the day of the week for the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INBOUND_I_CAL_STREAM">INBOUND_I_CAL_STREAM</a></strong></code>
<div class="block">
 Contains the contents of the iCalendar MIME part of the original MIME message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INFO_PATH_FORM_NAME">INFO_PATH_FORM_NAME</a></strong></code>
<div class="block">
 Contains the name of the form associated with this message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INSTANT_MESSAGING_ADDRESS">INSTANT_MESSAGING_ADDRESS</a></strong></code>
<div class="block">
 Specifies the instant messaging address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INTENDED_BUSY_STATUS">INTENDED_BUSY_STATUS</a></strong></code>
<div class="block">
 Contains the value of the PidLidBusyStatus property (section 2.47) on the Meeting object was sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INTERNET_ACCOUNT_NAME">INTERNET_ACCOUNT_NAME</a></strong></code>
<div class="block">
 Specifies the user-visible email account name through which the email message is sent.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INTERNET_ACCOUNT_STAMP">INTERNET_ACCOUNT_STAMP</a></strong></code>
<div class="block">
 Specifies the email account ID through which the email message is sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#IS_CONTACT_LINKED">IS_CONTACT_LINKED</a></strong></code>
<div class="block">
 Specifies whether the contact is linked to other contacts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#IS_EXCEPTION">IS_EXCEPTION</a></strong></code>
<div class="block">
 Indicates whether the object represents an exception (including an orphan instance).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#IS_RECURRING">IS_RECURRING</a></strong></code>
<div class="block">
 Specifies whether the object is associated with a recurring series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#IS_SILENT">IS_SILENT</a></strong></code>
<div class="block">
 Indicates whether the user did not include any text in the body of the Meeting Response object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LINKED_TASK_ITEMS">LINKED_TASK_ITEMS</a></strong></code>
<div class="block">
 Indicates whether the user did not include any text in the body of the Meeting Response object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOCATION">LOCATION</a></strong></code>
<div class="block">
 Specifies the location of the event.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_DOCUMENT_POSTED">LOG_DOCUMENT_POSTED</a></strong></code>
<div class="block">
 Indicates whether the document was sent by email or posted to a server folder during journaling.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_DOCUMENT_PRINTED">LOG_DOCUMENT_PRINTED</a></strong></code>
<div class="block">
 Indicates whether the document was printed during journaling.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_DOCUMENT_ROUTED">LOG_DOCUMENT_ROUTED</a></strong></code>
<div class="block">
 Indicates whether the document was sent to a routing recipient during journaling.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_DOCUMENT_SAVED">LOG_DOCUMENT_SAVED</a></strong></code>
<div class="block">
 Indicates whether the document was saved during journaling.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_DURATION">LOG_DURATION</a></strong></code>
<div class="block">
 Contains the duration, in minutes, of the activity.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_END">LOG_END</a></strong></code>
<div class="block">
 Contains the time, in UTC, at which the activity ended.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_FLAGS">LOG_FLAGS</a></strong></code>
<div class="block">
 Contains metadata about the Journal object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_START">LOG_START</a></strong></code>
<div class="block">
 Contains the time, in UTC, at which the activity began.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_TYPE">LOG_TYPE</a></strong></code>
<div class="block">
 Briefly describes the journal activity that is being recorded.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOG_TYPE_DESC">LOG_TYPE_DESC</a></strong></code>
<div class="block">
 Contains an expanded description of the journal activity that is being recorded.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MEETING_TYPE">MEETING_TYPE</a></strong></code>
<div class="block">
 Indicates the type of Meeting Request object or Meeting Update object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MEETING_WORKSPACE_URL">MEETING_WORKSPACE_URL</a></strong></code>
<div class="block">
 Specifies the URL of the Meeting Workspace that is associated with a Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MILEAGE">MILEAGE</a></strong></code>
<div class="block">
 Contains the mileage information that is associated with an item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MONTH_INTERVAL">MONTH_INTERVAL</a></strong></code>
<div class="block">
 Indicates the monthly interval of the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MONTH_OF_YEAR">MONTH_OF_YEAR</a></strong></code>
<div class="block">
 Indicates the month of the year in which the appointment or meeting occurs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MONTH_OF_YEAR_MASK">MONTH_OF_YEAR_MASK</a></strong></code>
<div class="block">
 Indicates the calculated month of the year in which the appointment or meeting occurs.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NET_SHOW_URL">NET_SHOW_URL</a></strong></code>
<div class="block">
 Specifies the URL to be launched when the user joins the meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NO_END_DATE_FLAG">NO_END_DATE_FLAG</a></strong></code>
<div class="block">
 Indicates whether the recurrence pattern has an end date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SEND_BCC_TRACK_STATUS">NON_SEND_BCC_TRACK_STATUS</a></strong></code>
<div class="block">
 Contains the value from the response table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SEND_CC_TRACK_STATUS">NON_SEND_CC_TRACK_STATUS</a></strong></code>
<div class="block">
 Contains the value from the response table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SEND_TO_TRACK_STATUS">NON_SEND_TO_TRACK_STATUS</a></strong></code>
<div class="block">
 Contains the value from the response table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SENDABLE_BCC">NON_SENDABLE_BCC</a></strong></code>
<div class="block">
 Contains a list of all of the unsendable attendees who are also resources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SENDABLE_CC">NON_SENDABLE_CC</a></strong></code>
<div class="block">
 Contains a list of all of the unsendable attendees who are also optional attendees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NON_SENDABLE_TO">NON_SENDABLE_TO</a></strong></code>
<div class="block">
 Contains a list of all of the unsendable attendees who are also required attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_COLOR">NOTE_COLOR</a></strong></code>
<div class="block">
 Specifies the suggested background color of the Note object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_HEIGHT">NOTE_HEIGHT</a></strong></code>
<div class="block">
 Specifies the height of the visible message window in pixels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_WIDTH">NOTE_WIDTH</a></strong></code>
<div class="block">
 Specifies the width of the visible message window in pixels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_X">NOTE_X</a></strong></code>
<div class="block">
 Specifies the distance, in pixels, from the left edge of the screen that a user interface displays a Note object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_Y">NOTE_Y</a></strong></code>
<div class="block">
 Specifies the distance, in pixels, from the top edge of the screen that a user interface displays a Note object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OCCURRENCES">OCCURRENCES</a></strong></code>
<div class="block">
 Indicates the number of occurrences in the recurring appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OLD_LOCATION">OLD_LOCATION</a></strong></code>
<div class="block">
 Indicates the original value of the PidLidLocation property (section 2.159) before a meeting update.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OLD_RECURRENCE_TYPE">OLD_RECURRENCE_TYPE</a></strong></code>
<div class="block">
 Indicates the recurrence pattern for the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OLD_WHEN_END_WHOLE">OLD_WHEN_END_WHOLE</a></strong></code>
<div class="block">
 Indicates the original value of the PidLidAppointmentEndWhole property (section 2.14) before a meeting update.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OLD_WHEN_START_WHOLE">OLD_WHEN_START_WHOLE</a></strong></code>
<div class="block">
 Indicates the original value of the PidLidAppointmentStartWhole property (section 2.29) before a meeting update.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ONLINE_PASSWORD">ONLINE_PASSWORD</a></strong></code>
<div class="block">
 Specifies the password for a meeting on which the PidLidConferencingType property (section 2.66) has the value 0x00000002.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OPTIONAL_ATTENDEES">OPTIONAL_ATTENDEES</a></strong></code>
<div class="block">
 Specifies optional attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ORGANIZER_ALIAS">ORGANIZER_ALIAS</a></strong></code>
<div class="block">
 Specifies the email address of the organizer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ORIGINAL_STORE_ENTRY_ID">ORIGINAL_STORE_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the EntryID of the delegator�s message store.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OTHER_ADDRESS">OTHER_ADDRESS</a></strong></code>
<div class="block">
 Specifies the complete address of the other address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OTHER_ADDRESS_COUNTRY_CODE">OTHER_ADDRESS_COUNTRY_CODE</a></strong></code>
<div class="block">
 Specifies the country code portion of the other address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OWNER_CRITICAL_CHANGE">OWNER_CRITICAL_CHANGE</a></strong></code>
<div class="block">
 Specifies the date and time at which a Meeting Request object was sent by the organizer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OWNER_NAME">OWNER_NAME</a></strong></code>
<div class="block">
 Indicates the name of the owner of the mailbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PENDING_STATE_FOR_SITE_MAILBOX_DOCUMENT">PENDING_STATE_FOR_SITE_MAILBOX_DOCUMENT</a></strong></code>
<div class="block">
 Specifies the synchronization state of the Document object that is in the Document Libraries folder of the site mailbox.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PERCENT_COMPLETE">PERCENT_COMPLETE</a></strong></code>
<div class="block">
 Indicates whether a time-flagged Message object is complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_CHANNEL">POST_RSS_CHANNEL</a></strong></code>
<div class="block">
 Contains the contents of the title field from the XML of the Atom feed or RSS channel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_CHANNEL_LINK">POST_RSS_CHANNEL_LINK</a></strong></code>
<div class="block">
 Contains the URL of the RSS or Atom feed from which the XML file came.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_ITEM_GUID">POST_RSS_ITEM_GUID</a></strong></code>
<div class="block">
 Contains a unique identifier for the RSS object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_ITEM_HASH">POST_RSS_ITEM_HASH</a></strong></code>
<div class="block">
 Contains a hash of the feed XML computed by using an implementation-dependent algorithm.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_ITEM_LINK">POST_RSS_ITEM_LINK</a></strong></code>
<div class="block">
 Contains the URL of the link from an RSS or Atom item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_ITEM_XML">POST_RSS_ITEM_XML</a></strong></code>
<div class="block">
 Contains the item element and all of its sub-elements from an RSS feed, or the entry element and all of its sub-elements from an Atom feed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POST_RSS_SUBSCRIPTION">POST_RSS_SUBSCRIPTION</a></strong></code>
<div class="block">
 Contains the user's preferred name for the RSS or Atom subscription.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#POSTAL_ADDRESS_ID">POSTAL_ADDRESS_ID</a></strong></code>
<div class="block">
 Specifies which physical address is the mailing address for this contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PRIVATE">PRIVATE</a></strong></code>
<div class="block">
 Indicates whether the end user wishes for this Message object to be hidden from other users who have access to the Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PROMPT_SEND_UPDATE">PROMPT_SEND_UPDATE</a></strong></code>
<div class="block">
 Indicates that the Meeting Response object was out-of-date when it was received.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RECURRENCE_DURATION">RECURRENCE_DURATION</a></strong></code>
<div class="block">
 Identifies the length, in minutes, of the appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RECURRENCE_PATTERN">RECURRENCE_PATTERN</a></strong></code>
<div class="block">
 Specifies a description of the recurrence pattern of the Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RECURRENCE_TYPE">RECURRENCE_TYPE</a></strong></code>
<div class="block">
 Specifies the recurrence type of the recurring series.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RECURRING">RECURRING</a></strong></code>
<div class="block">
 Specifies whether the object represents a recurring series.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REFERENCE_ENTRY_ID">REFERENCE_ENTRY_ID</a></strong></code>
<div class="block">
 Specifies the value of the EntryID of the Contact object unless the Contact object is a copy of an earlier original.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_DELTA">REMINDER_DELTA</a></strong></code>
<div class="block">
 Specifies the interval, in minutes, between the time at which the reminder first becomes overdue and the start time of the Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_FILE_PARAMETER">REMINDER_FILE_PARAMETER</a></strong></code>
<div class="block">
 Specifies the filename of the sound that a client is to play when the reminder for that object becomes overdue.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_OVERRIDE">REMINDER_OVERRIDE</a></strong></code>
<div class="block">
 Specifies whether the client is to respect the current values of the property (section 2.219), or use the default values for those properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_PLAY_SOUND">REMINDER_PLAY_SOUND</a></strong></code>
<div class="block">
 Specifies whether the client is to play a sound when the reminder becomes overdue.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_SET">REMINDER_SET</a></strong></code>
<div class="block">
 Specifies whether a reminder is set on the object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_SIGNAL_TIME">REMINDER_SIGNAL_TIME</a></strong></code>
<div class="block">
 Specifies the point in time when a reminder transitions from pending to overdue.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_TIME">REMINDER_TIME</a></strong></code>
<div class="block">
 Specifies the initial signal time for objects that are not Calendar objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_TIME_DATE">REMINDER_TIME_DATE</a></strong></code>
<div class="block">
 Indicates the time and date of the reminder for the appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_TIME_TIME">REMINDER_TIME_TIME</a></strong></code>
<div class="block">
 Indicates the time of the reminder for the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMINDER_TYPE">REMINDER_TYPE</a></strong></code>
<div class="block">
 This property is not set and, if set, is ignored.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REMOTE_STATUS">REMOTE_STATUS</a></strong></code>
<div class="block">
 Indicates the remote status of the calendar item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REQUIRED_ATTENDEES">REQUIRED_ATTENDEES</a></strong></code>
<div class="block">
 Identifies required attendees for the appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RESOURCE_ATTENDEES">RESOURCE_ATTENDEES</a></strong></code>
<div class="block">
 Identifies resource attendees for the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RESPONSE_STATUS">RESPONSE_STATUS</a></strong></code>
<div class="block">
 Specifies the response status of an attendee.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SERVER_PROCESSED">SERVER_PROCESSED</a></strong></code>
<div class="block">
 Indicates whether the Meeting Request object or Meeting Update object has been processed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SERVER_PROCESSING_ACTIONS">SERVER_PROCESSING_ACTIONS</a></strong></code>
<div class="block">
 Indicates what processing actions have been taken on this Meeting Request object or Meeting Update object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_ANONYMITY">SHARING_ANONYMITY</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_BINDING_ENTRY_ID">SHARING_BINDING_ENTRY_ID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_BROWSE_URL">SHARING_BROWSE_URL</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_CAPABILITIES">SHARING_CAPABILITIES</a></strong></code>
<div class="block">
 Indicates that the Message object relates to a special folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_CONFIGURATION_URL">SHARING_CONFIGURATION_URL</a></strong></code>
<div class="block">
 Contains a zero-length string.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_DATA_RANGE_END">SHARING_DATA_RANGE_END</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_DATA_RANGE_START">SHARING_DATA_RANGE_START</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_DETAIL">SHARING_DETAIL</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_EXTENSION_XML">SHARING_EXTENSION_XML</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_FILTER">SHARING_FILTER</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_FLAGS">SHARING_FLAGS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_FLAVOR">SHARING_FLAVOR</a></strong></code>
<div class="block">
 Indicates the type of Sharing Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_FOLDER_ENTRY_ID">SHARING_FOLDER_ENTRY_ID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_INDEX_ENTRY_ID">SHARING_INDEX_ENTRY_ID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_INITIATOR_ENTRY_ID">SHARING_INITIATOR_ENTRY_ID</a></strong></code>
<div class="block">
 Contains the value of the PidTagEntryId property (section 2.674) for the Address Book object of the currently logged-on user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_INITIATOR_NAME">SHARING_INITIATOR_NAME</a></strong></code>
<div class="block">
 Contains the value of the PidTagDisplayName property (section 2.667) from the Address Book object identified by the PidLidSharingInitiatorEntryId property (section 2.248).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_INITIATOR_SMTP">SHARING_INITIATOR_SMTP</a></strong></code>
<div class="block">
 Contains the value of the PidTagSmtpAddress property (section 2.1010) from the Address Book object identified by the PidLidSharingInitiatorEntryId property (section 2.248).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_INSTANCE_GUID">SHARING_INSTANCE_GUID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LAST_AUTO_SYNC_TIME">SHARING_LAST_AUTO_SYNC_TIME</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LAST_SYNC_TIME">SHARING_LAST_SYNC_TIME</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_COMMENT">SHARING_LOCAL_COMMENT</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_LAST_MODIFICATION_TIME">SHARING_LOCAL_LAST_MODIFICATION_TIME</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_NAME">SHARING_LOCAL_NAME</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_PATH">SHARING_LOCAL_PATH</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_STORE_UID">SHARING_LOCAL_STORE_UID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_TYPE">SHARING_LOCAL_TYPE</a></strong></code>
<div class="block">
 Contains the value of the PidTagContainerClass property (section 2.633) of the folder being shared.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_LOCAL_UID">SHARING_LOCAL_UID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_ORIGINAL_MESSAGE_ENTRY_ID">SHARING_ORIGINAL_MESSAGE_ENTRY_ID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PARENT_BINDING_ENTRY_ID">SHARING_PARENT_BINDING_ENTRY_ID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PARTICIPANTS">SHARING_PARTICIPANTS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PERMISSIONS">SHARING_PERMISSIONS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PROVIDER_EXTENSION">SHARING_PROVIDER_EXTENSION</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PROVIDER_GUID">SHARING_PROVIDER_GUID</a></strong></code>
<div class="block">
 Contains the value "%xAE.F0.***********.00.00.C0.***********.00.00.46".</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PROVIDER_NAME">SHARING_PROVIDER_NAME</a></strong></code>
<div class="block">
 Contains a user-displayable name of the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_PROVIDER_URL">SHARING_PROVIDER_URL</a></strong></code>
<div class="block">
 Contains a URL related to the sharing provider identified by the PidLidSharingProviderGuid property (section 2.266).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_RANGE_END">SHARING_RANGE_END</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_RANGE_START">SHARING_RANGE_START</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_RECIPROCATION">SHARING_RECIPROCATION</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_BYTE_SIZE">SHARING_REMOTE_BYTE_SIZE</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_COMMENT">SHARING_REMOTE_COMMENT</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_CRC">SHARING_REMOTE_CRC</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_LAST_MODIFICATION_TIME">SHARING_REMOTE_LAST_MODIFICATION_TIME</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_MESSAGE_COUNT">SHARING_REMOTE_MESSAGE_COUNT</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_NAME">SHARING_REMOTE_NAME</a></strong></code>
<div class="block">
 Contains the value of the PidTagDisplayName property (section 2.667) on the folder being shared.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_PASS">SHARING_REMOTE_PASS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_PATH">SHARING_REMOTE_PATH</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_STORE_UID">SHARING_REMOTE_STORE_UID</a></strong></code>
<div class="block">
 Contains a hexadecimal string representation of the value of the PidTagStoreEntryId property (section 2.1018) on the folder being shared.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_TYPE">SHARING_REMOTE_TYPE</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingLocalType property (section 2.259).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_UID">SHARING_REMOTE_UID</a></strong></code>
<div class="block">
 Contains the EntryID of the folder being shared.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_USER">SHARING_REMOTE_USER</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_REMOTE_VERSION">SHARING_REMOTE_VERSION</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_RESPONSE_TIME">SHARING_RESPONSE_TIME</a></strong></code>
<div class="block">
 Contains the time at which the recipient of the sharing request sent a sharing response.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_RESPONSE_TYPE">SHARING_RESPONSE_TYPE</a></strong></code>
<div class="block">
 Contains the type of response with which the recipient of the sharing request responded.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_ROAM_LOG">SHARING_ROAM_LOG</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_START">SHARING_START</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_STATUS">SHARING_STATUS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_STOP">SHARING_STOP</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_SYNC_FLAGS">SHARING_SYNC_FLAGS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_SYNC_INTERVAL">SHARING_SYNC_INTERVAL</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_TIME_TO_LIVE">SHARING_TIME_TO_LIVE</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_TIME_TO_LIVE_AUTO">SHARING_TIME_TO_LIVE_AUTO</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_WORKING_HOURS_DAYS">SHARING_WORKING_HOURS_DAYS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_WORKING_HOURS_END">SHARING_WORKING_HOURS_END</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_WORKING_HOURS_START">SHARING_WORKING_HOURS_START</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SHARING_WORKING_HOURS_TIME_ZONE">SHARING_WORKING_HOURS_TIME_ZONE</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SIDE_EFFECTS">SIDE_EFFECTS</a></strong></code>
<div class="block">
 Specifies how a Message object is handled by the client in relation to certain user interface actions by the user, such as deleting a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SINGLE_BODY_I_CAL">SINGLE_BODY_I_CAL</a></strong></code>
<div class="block">
 Indicates that the original MIME message contained a single MIME part.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SMART_NO_ATTACH">SMART_NO_ATTACH</a></strong></code>
<div class="block">
 Indicates whether the Message object has no end-user visible attachments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SPAM_ORIGINAL_FOLDER">SPAM_ORIGINAL_FOLDER</a></strong></code>
<div class="block">
 Specifies which folder a message was in before it was filtered into the Junk Email folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#START_RECURRENCE_DATE">START_RECURRENCE_DATE</a></strong></code>
<div class="block">
 Identifies the start date of the recurrence pattern.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#START_RECURRENCE_TIME">START_RECURRENCE_TIME</a></strong></code>
<div class="block">
 Identifies the start time of the recurrence pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ACCEPTANCE_STATE">TASK_ACCEPTANCE_STATE</a></strong></code>
<div class="block">
 Indicates the acceptance state of the task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ACCEPTED">TASK_ACCEPTED</a></strong></code>
<div class="block">
 Indicates whether a task assignee has replied to a task request for this Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ACTUAL_EFFORT">TASK_ACTUAL_EFFORT</a></strong></code>
<div class="block">
 Indicates the number of minutes that the user actually spent working on a task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ASSIGNER">TASK_ASSIGNER</a></strong></code>
<div class="block">
 Specifies the name of the user that last assigned the task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ASSIGNERS">TASK_ASSIGNERS</a></strong></code>
<div class="block">
 Contains a stack of entries, each of which represents a task assigner.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_COMPLETE">TASK_COMPLETE</a></strong></code>
<div class="block">
 Indicates that the task is complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_CUSTOM_FLAGS">TASK_CUSTOM_FLAGS</a></strong></code>
<div class="block">
 The client can set this property, but it has no impact on the Task-Related Objects Protocol and is ignored by the server.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_DATE_COMPLETED">TASK_DATE_COMPLETED</a></strong></code>
<div class="block">
 Specifies the date when the user completed work on the task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_DEAD_OCCURRENCE">TASK_DEAD_OCCURRENCE</a></strong></code>
<div class="block">
 Indicates whether new occurrences remain to be generated.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_DUE_DATE">TASK_DUE_DATE</a></strong></code>
<div class="block">
 Specifies the date by which the user expects work on the task to be complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ESTIMATED_EFFORT">TASK_ESTIMATED_EFFORT</a></strong></code>
<div class="block">
 Indicates the number of minutes that the user expects to work on a task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_F_CREATOR">TASK_F_CREATOR</a></strong></code>
<div class="block">
 Indicates that the Task object was originally created by the action of the current user or user agent instead of by the processing of a task request.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_F_FIX_OFFLINE">TASK_F_FIX_OFFLINE</a></strong></code>
<div class="block">
 Indicates the accuracy of the PidLidTaskOwner property (section 2.328).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_F_RECURRING">TASK_F_RECURRING</a></strong></code>
<div class="block">
 Indicates whether the task includes a recurrence pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_GLOBAL_ID">TASK_GLOBAL_ID</a></strong></code>
<div class="block">
 Contains a unique GUID for this task, which is used to locate an existing task upon receipt of a task response or task update.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_HISTORY">TASK_HISTORY</a></strong></code>
<div class="block">
 Indicates the type of change that was last made to the Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_LAST_DELEGATE">TASK_LAST_DELEGATE</a></strong></code>
<div class="block">
 Contains the name of the user who most recently assigned the task, or the user to whom it was most recently assigned.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_LAST_UPDATE">TASK_LAST_UPDATE</a></strong></code>
<div class="block">
 Contains the date and time of the most recent change made to the Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_LAST_USER">TASK_LAST_USER</a></strong></code>
<div class="block">
 Contains the name of the most recent user to have been the owner of the task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_MODE">TASK_MODE</a></strong></code>
<div class="block">
 Specifies the assignment status of the embedded Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_MULTIPLE_RECIPIENTS">TASK_MULTIPLE_RECIPIENTS</a></strong></code>
<div class="block">
 Provides optimization hints about the recipients of a Task object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_NO_COMPUTE">TASK_NO_COMPUTE</a></strong></code>
<div class="block">
 Not used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ORDINAL">TASK_ORDINAL</a></strong></code>
<div class="block">
 Provides an aid to custom sorting of Task objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_OWNER">TASK_OWNER</a></strong></code>
<div class="block">
 Contains the name of the owner of the task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_OWNERSHIP">TASK_OWNERSHIP</a></strong></code>
<div class="block">
 Indicates the role of the current user relative to the Task object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_RECURRENCE">TASK_RECURRENCE</a></strong></code>
<div class="block">
 Contains a RecurrencePattern structure that provides information about recurring tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_RESET_REMINDER">TASK_RESET_REMINDER</a></strong></code>
<div class="block">
 Indicates whether future instances of recurring tasks need reminders, even though the value of the PidLidReminderSet property (section 2.222) is 0x00.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_ROLE">TASK_ROLE</a></strong></code>
<div class="block">
 Not used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_START_DATE">TASK_START_DATE</a></strong></code>
<div class="block">
 Specifies the date on which the user expects work on the task to begin.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_STATE">TASK_STATE</a></strong></code>
<div class="block">
 Indicates the current assignment state of the Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_STATUS">TASK_STATUS</a></strong></code>
<div class="block">
 Specifies the status of a task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_STATUS_ON_COMPLETE">TASK_STATUS_ON_COMPLETE</a></strong></code>
<div class="block">
 Indicates whether the task assignee has been requested to send an email message update upon completion of the assigned task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_UPDATES">TASK_UPDATES</a></strong></code>
<div class="block">
 Indicates whether the task assignee has been requested to send a task update when the assigned Task object changes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TASK_VERSION">TASK_VERSION</a></strong></code>
<div class="block">
 Indicates which copy is the latest update of a Task object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TEAM_TASK">TEAM_TASK</a></strong></code>
<div class="block">
 This property is set by the client but is ignored by the server.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TIME_ZONE">TIME_ZONE</a></strong></code>
<div class="block">
 Specifies information about the time zone of a recurring meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TIME_ZONE_DESCRIPTION">TIME_ZONE_DESCRIPTION</a></strong></code>
<div class="block">
 Specifies a human-readable description of the time zone that is represented by the data in the PidLidTimeZoneStruct property (section 2.342).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TIME_ZONE_STRUCT">TIME_ZONE_STRUCT</a></strong></code>
<div class="block">
 Specifies time zone information for a recurring meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TO_ATTENDEES_STRING">TO_ATTENDEES_STRING</a></strong></code>
<div class="block">
 Contains a list of all of the sendable attendees who are also required attendees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TO_DO_ORDINAL_DATE">TO_DO_ORDINAL_DATE</a></strong></code>
<div class="block">
 Contains the current time, in UTC, which is used to determine the sort order of objects in a consolidated to-do list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TO_DO_SUB_ORDINAL">TO_DO_SUB_ORDINAL</a></strong></code>
<div class="block">
 Contains the numerals 0 through 9 that are used to break a tie when the PidLidToDoOrdinalDate property (section 2.344) is used to perform a sort of objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TO_DO_TITLE">TO_DO_TITLE</a></strong></code>
<div class="block">
 Contains user-specifiable text to identify this Message object in a consolidated to-do list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#USE_TNEF">USE_TNEF</a></strong></code>
<div class="block">
 Specifies whether Transport Neutral Encapsulation Format (TNEF) is to be included on a message when the message is converted from TNEF to MIME or SMTP format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#VALID_FLAG_STRING_PROOF">VALID_FLAG_STRING_PROOF</a></strong></code>
<div class="block">
 Contains the value of the PidTagMessageDeliveryTime property (section 2.780) when modifying the PidLidFlagRequest property (section 2.136).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#VERB_RESPONSE">VERB_RESPONSE</a></strong></code>
<div class="block">
 Specifies the voting option that a respondent has selected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#VERB_STREAM">VERB_STREAM</a></strong></code>
<div class="block">
 Specifies what voting responses the user can make in response to the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WEDDING_ANNIVERSARY_LOCAL">WEDDING_ANNIVERSARY_LOCAL</a></strong></code>
<div class="block">
 Specifies the wedding anniversary of the contact, at midnight in the client's local time zone, and is saved without any time zone conversions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WEEK_INTERVAL">WEEK_INTERVAL</a></strong></code>
<div class="block">
 Identifies the number of weeks that occur between each meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WHERE">WHERE</a></strong></code>
<div class="block">
 Contains the value of the PidLidLocation property (section 2.159) from the associated Meeting object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS">WORK_ADDRESS</a></strong></code>
<div class="block">
 Specifies the complete address of the work address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_CITY">WORK_ADDRESS_CITY</a></strong></code>
<div class="block">
 Specifies the city or locality portion of the work address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_COUNTRY">WORK_ADDRESS_COUNTRY</a></strong></code>
<div class="block">
 Specifies the country or region portion of the work address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_COUNTRY_CODE">WORK_ADDRESS_COUNTRY_CODE</a></strong></code>
<div class="block">
 Specifies the country code portion of the work address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_POST_OFFICE_BOX">WORK_ADDRESS_POST_OFFICE_BOX</a></strong></code>
<div class="block">
 Specifies the post office box portion of the work address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_POSTAL_CODE">WORK_ADDRESS_POSTAL_CODE</a></strong></code>
<div class="block">
 Specifies the postal code (ZIP code) portion of the work address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_STATE">WORK_ADDRESS_STATE</a></strong></code>
<div class="block">
 Specifies the state or province portion of the work address of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORK_ADDRESS_STREET">WORK_ADDRESS_STREET</a></strong></code>
<div class="block">
 Specifies the street portion of the work address of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#YEAR_INTERVAL">YEAR_INTERVAL</a></strong></code>
<div class="block">
 Indicates the yearly interval of the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#YOMI_COMPANY_NAME">YOMI_COMPANY_NAME</a></strong></code>
<div class="block">
 Specifies the phonetic pronunciation of the company name of the contact.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#YOMI_FIRST_NAME">YOMI_FIRST_NAME</a></strong></code>
<div class="block">
 Specifies the phonetic pronunciation of the given name of the contact.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#YOMI_LAST_NAME">YOMI_LAST_NAME</a></strong></code>
<div class="block">
 Specifies the phonetic pronunciation of the surname of the contact.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(long,%20int,%20java.util.UUID)">find</a></strong>(long&nbsp;lid,
    int&nbsp;type,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Finds <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email"><code>PidLidPropertyDescriptor</code></a> property in list according to required parameters</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(long,%20java.util.UUID)">find</a></strong>(long&nbsp;lid,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Finds <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email"><code>PidLidPropertyDescriptor</code></a> property in list according to required parameters
 This is simplified search operation without data type comparison.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#getInstance(long,%20int,%20java.util.UUID)">getInstance</a></strong>(long&nbsp;lid,
           int&nbsp;dataType,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Retrieves <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email"><code>PidLidPropertyDescriptor</code></a> object</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidLidPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html#op_Equality(com.aspose.email.PidLidPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidLidPropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html#op_Inequality(com.aspose.email.PidLidPropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiNamedProperty.html#MapiNamedProperty(com.aspose.email.INamedPropertyTagProvider,%20com.aspose.email.PidLidPropertyDescriptor,%20java.lang.Object)">MapiNamedProperty</a></strong>(<a href="../../../../com/aspose/email/INamedPropertyTagProvider.html" title="interface in com.aspose.email">INamedPropertyTagProvider</a>&nbsp;tagProvider,
                 <a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;data)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiNamedProperty.html" title="class in com.aspose.email"><code>MapiNamedProperty</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiProperty.html#MapiProperty(com.aspose.email.PidLidPropertyDescriptor,%20java.lang.Object)">MapiProperty</a></strong>(<a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">PidLidPropertyDescriptor</a>&nbsp;pd,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;data)</code>
<div class="block">
 Initializes a new instance of the MapiProperty class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PidLidPropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PidLidPropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PidLidPropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
