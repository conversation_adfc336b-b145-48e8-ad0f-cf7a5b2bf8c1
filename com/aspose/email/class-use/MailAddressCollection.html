<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MailAddressCollection (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MailAddressCollection (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailAddressCollection.html" target="_top">Frames</a></li>
<li><a href="MailAddressCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MailAddressCollection" class="title">Uses of Class<br>com.aspose.email.MailAddressCollection</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#attendeesfield">attendeesfield</a></strong></code>
<div class="block">Task attendees</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#copy()">copy</a></strong>()</code>
<div class="block">
 Copies this instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#expandDistributionList(com.aspose.email.MailAddress)">expandDistributionList</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;mailAddress)</code>
<div class="block">
 Expands the public Distribution List members.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchDistributionList(com.aspose.email.ExchangeDistributionList)">fetchDistributionList</a></strong>(<a href="../../../../com/aspose/email/ExchangeDistributionList.html" title="class in com.aspose.email">ExchangeDistributionList</a>&nbsp;distributionList)</code>
<div class="block">
 Fetches the private Distribution List members.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#getAttendees()">getAttendees</a></strong>()</code>
<div class="block">
 Gets or sets the attendees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#getAttendees()">getAttendees</a></strong>()</code>
<div class="block">
 Gets or sets the attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 Gets or sets the address collection 
 that contains the BCC recipients of message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MboxMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MboxMessageInfo.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 Gets the address collection 
 that contains BCC recipients of message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 Gets blind carbon copy of the E-Mail message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getCC()">getCC</a></strong>()</code>
<div class="block">
 Gets or sets the address collection 
 that contains the CC recipients</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MboxMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MboxMessageInfo.html#getCC()">getCC</a></strong>()</code>
<div class="block">
 Gets the address collection 
 that contains CC recipients</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getCC()">getCC</a></strong>()</code>
<div class="block">
 Gets CC of the E-Mail message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#getForwardAsAttachmentToRecipients()">getForwardAsAttachmentToRecipients</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be forwarded as attachments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#getForwardToRecipients()">getForwardToRecipients</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be forwarded.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RulePredicates.</span><code><strong><a href="../../../../com/aspose/email/RulePredicates.html#getFromAddresses()">getFromAddresses</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses of the senders of incoming messages in order for the condition or exception to apply.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#getOptionalAttendees()">getOptionalAttendees</a></strong>()</code>
<div class="block">
 Gets the optional attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageTrackingReport.</span><code><strong><a href="../../../../com/aspose/email/MessageTrackingReport.html#getOriginalRecipients()">getOriginalRecipients</a></strong>()</code>
<div class="block">
 Gets the e-mail addresses of the message recipients.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getReadReceiptTo()">getReadReceiptTo</a></strong>()</code>
<div class="block">
 Gets or sets the read receipt address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">GetMailTipsOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#getRecipients()">getRecipients</a></strong>()</code>
<div class="block">
 Gets or sets a list of recipients to check for mail tips.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageTrackingReportInfo.</span><code><strong><a href="../../../../com/aspose/email/MessageTrackingReportInfo.html#getRecipients()">getRecipients</a></strong>()</code>
<div class="block">
 Gets the e-mail addresses of the recipients for the message that was found.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#getRedirectToRecipients()">getRedirectToRecipients</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be redirected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getReplyTo()">getReplyTo</a></strong>()</code>
<div class="block">
 Gets the list of addresses that should receive replies to this message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getReplyToList()">getReplyToList</a></strong>()</code>
<div class="block">
 Gets or sets the list of addresses 
 to reply to for the mail message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#getSendSMSAlertToRecipients()">getSendSMSAlertToRecipients</a></strong>()</code>
<div class="block">
 Gets or sets the mobile phone numbers to which a Short Message Service (SMS) alert is to be sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">RulePredicates.</span><code><strong><a href="../../../../com/aspose/email/RulePredicates.html#getSentToAddresses()">getSentToAddresses</a></strong>()</code>
<div class="block">
 Gets or sets the e-mail addresses that incoming messages have to have been sent to in order for the condition or exception to apply.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Gets or sets the address collection that contains 
 the recipients of message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MboxMessageInfo.</span><code><strong><a href="../../../../com/aspose/email/MboxMessageInfo.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Gets the address collection that contains 
 the recipients of message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MessageInfoBase.</span><code><strong><a href="../../../../com/aspose/email/MessageInfoBase.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Gets the receiptants of the E-Mail message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#to_MailAddressCollection(com.aspose.email.MailAddress)">to_MailAddressCollection</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;address)</code>
<div class="block">
 Performs an implicit conversion from <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a> to <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email"><code>MailAddressCollection</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#to_MailAddressCollection(java.lang.String)">to_MailAddressCollection</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;addresses)</code>
<div class="block">
 Performs an implicit conversion from <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a> to <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email"><code>MailAddressCollection</code></a>.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailAddressCollection.</span><code><strong><a href="../../../../com/aspose/email/MailAddressCollection.html#addRange(com.aspose.email.MailAddressCollection)">addRange</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;addresses)</code>
<div class="block">
 Adds addresses to collection</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#addToDistributionList(com.aspose.email.ExchangeDistributionList,%20com.aspose.email.MailAddressCollection)">addToDistributionList</a></strong>(<a href="../../../../com/aspose/email/ExchangeDistributionList.html" title="class in com.aspose.email">ExchangeDistributionList</a>&nbsp;distributionList,
                     <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;members)</code>
<div class="block">
 Appends the members to Distribution List.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createDistributionList(com.aspose.email.ExchangeDistributionList,%20com.aspose.email.MailAddressCollection)">createDistributionList</a></strong>(<a href="../../../../com/aspose/email/ExchangeDistributionList.html" title="class in com.aspose.email">ExchangeDistributionList</a>&nbsp;distributionList,
                      <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;members)</code>
<div class="block">
 Creates the private Distribution List.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#deleteFromDistributionList(com.aspose.email.ExchangeDistributionList,%20com.aspose.email.MailAddressCollection)">deleteFromDistributionList</a></strong>(<a href="../../../../com/aspose/email/ExchangeDistributionList.html" title="class in com.aspose.email">ExchangeDistributionList</a>&nbsp;distributionList,
                          <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;members)</code>
<div class="block">
 Deletes the members from Distribution List.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20java.io.InputStream)">forward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;messageStream)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20java.io.InputStream)">forward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;messageStream)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#setAttendees(com.aspose.email.MailAddressCollection)">setAttendees</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the attendees.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Task.</span><code><strong><a href="../../../../com/aspose/email/Task.html#setAttendees(com.aspose.email.MailAddressCollection)">setAttendees</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the attendees.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setBcc(com.aspose.email.MailAddressCollection)">setBcc</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the address collection 
 that contains the BCC recipients of message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setCC(com.aspose.email.MailAddressCollection)">setCC</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the address collection 
 that contains the CC recipients</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#setForwardAsAttachmentToRecipients(com.aspose.email.MailAddressCollection)">setForwardAsAttachmentToRecipients</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be forwarded as attachments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#setForwardToRecipients(com.aspose.email.MailAddressCollection)">setForwardToRecipients</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be forwarded.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RulePredicates.</span><code><strong><a href="../../../../com/aspose/email/RulePredicates.html#setFromAddresses(com.aspose.email.MailAddressCollection)">setFromAddresses</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses of the senders of incoming messages in order for the condition or exception to apply.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setReadReceiptTo(com.aspose.email.MailAddressCollection)">setReadReceiptTo</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the read receipt address.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">GetMailTipsOptions.</span><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#setRecipients(com.aspose.email.MailAddressCollection)">setRecipients</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets a list of recipients to check for mail tips.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#setRedirectToRecipients(com.aspose.email.MailAddressCollection)">setRedirectToRecipients</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses to which messages are to be redirected.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setReplyToList(com.aspose.email.MailAddressCollection)">setReplyToList</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the list of addresses 
 to reply to for the mail message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RuleActions.</span><code><strong><a href="../../../../com/aspose/email/RuleActions.html#setSendSMSAlertToRecipients(com.aspose.email.MailAddressCollection)">setSendSMSAlertToRecipients</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the mobile phone numbers to which a Short Message Service (SMS) alert is to be sent.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">RulePredicates.</span><code><strong><a href="../../../../com/aspose/email/RulePredicates.html#setSentToAddresses(com.aspose.email.MailAddressCollection)">setSentToAddresses</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the e-mail addresses that incoming messages have to have been sent to in order for the condition or exception to apply.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#setTo(com.aspose.email.MailAddressCollection)">setTo</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the address collection that contains 
 the recipients of message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a></code></td>
<td class="colLast"><span class="strong">MailAddress.</span><code><strong><a href="../../../../com/aspose/email/MailAddress.html#to_MailAddress(com.aspose.email.MailAddressCollection)">to_MailAddress</a></strong>(<a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;addresses)</code>
<div class="block">
 Performs an implicit conversion from <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email"><code>MailAddressCollection</code></a> to <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email"><code>MailAddress</code></a>.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20java.lang.String)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uid)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20java.lang.String,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uid,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/GetMailTipsOptions.html#GetMailTipsOptions(com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20int)">GetMailTipsOptions</a></strong>(<a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;sendingAs,
                  <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
                  int&nbsp;mailTipsRequested)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/GetMailTipsOptions.html" title="class in com.aspose.email"><code>GetMailTipsOptions</code></a> class</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailAddressCollection.html" target="_top">Frames</a></li>
<li><a href="MailAddressCollection.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
