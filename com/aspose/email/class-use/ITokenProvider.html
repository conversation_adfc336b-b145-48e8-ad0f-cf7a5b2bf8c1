<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface com.aspose.email.ITokenProvider (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Interface com.aspose.email.ITokenProvider (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ITokenProvider.html" target="_top">Frames</a></li>
<li><a href="ITokenProvider.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.aspose.email.ITokenProvider" class="title">Uses of Interface<br>com.aspose.email.ITokenProvider</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that implement <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/TokenProvider.html" title="class in com.aspose.email">TokenProvider</a></strong></code>
<div class="block">
 Class TokenProvider allows to retrieve access token for mail services.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></code></td>
<td class="colLast"><span class="strong">EmailClient.</span><code><strong><a href="../../../../com/aspose/email/EmailClient.html#getTokenProvider()">getTokenProvider</a></strong>()</code>
<div class="block">
 Gets or sets TokenProvider allowing to retrieve access token.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></code></td>
<td class="colLast"><span class="strong">GraphClient.</span><code><strong><a href="../../../../com/aspose/email/GraphClient.html#getTokenProvider()">getTokenProvider</a></strong>()</code>
<div class="block">
 Gets or sets an object allows to retrieve OAuth access token.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#getTokenProvider()">getTokenProvider</a></strong>()</code>
<div class="block">
 Gets or sets an object allows to retrieve OAuth access token.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></code></td>
<td class="colLast"><span class="strong">OAuthNetworkCredential.</span><code><strong><a href="../../../../com/aspose/email/OAuthNetworkCredential.html#getTokenProvider()">getTokenProvider</a></strong>()</code>
<div class="block">
 Gets the token provider.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/IGraphClient.html" title="interface in com.aspose.email">IGraphClient</a></code></td>
<td class="colLast"><span class="strong">GraphClient.</span><code><strong><a href="../../../../com/aspose/email/GraphClient.html#getClient(com.aspose.email.ITokenProvider)">getClient</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/GraphClient.html" title="class in com.aspose.email"><code>GraphClient</code></a> based class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/IGraphClient.html" title="interface in com.aspose.email">IGraphClient</a></code></td>
<td class="colLast"><span class="strong">GraphClient.</span><code><strong><a href="../../../../com/aspose/email/GraphClient.html#getClient(com.aspose.email.ITokenProvider,%20java.lang.String)">getClient</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tenantId)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/GraphClient.html" title="class in com.aspose.email"><code>GraphClient</code></a> based class</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">EmailClient.</span><code><strong><a href="../../../../com/aspose/email/EmailClient.html#setTokenProvider(com.aspose.email.ITokenProvider)">setTokenProvider</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;value)</code>
<div class="block">
 Gets or sets TokenProvider allowing to retrieve access token.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">GraphClient.</span><code><strong><a href="../../../../com/aspose/email/GraphClient.html#setTokenProvider(com.aspose.email.ITokenProvider)">setTokenProvider</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;value)</code>
<div class="block">
 Gets or sets an object allows to retrieve OAuth access token.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#setTokenProvider(com.aspose.email.ITokenProvider)">setTokenProvider</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;value)</code>
<div class="block">
 Gets or sets an object allows to retrieve OAuth access token.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/EmailClient.html#EmailClient(java.lang.String,%20java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider)">EmailClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
           int&nbsp;port,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
           <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/EmailClient.html" title="class in com.aspose.email"><code>EmailClient</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/EmailClient.html#EmailClient(java.lang.String,%20java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">EmailClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
           int&nbsp;port,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
           <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
           int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/EmailClient.html" title="class in com.aspose.email"><code>EmailClient</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapClient.html#ImapClient(java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider)">ImapClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          int&nbsp;port,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapClient.html" title="class in com.aspose.email"><code>ImapClient</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapClient.html#ImapClient(java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">ImapClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          int&nbsp;port,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
          int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapClient.html" title="class in com.aspose.email"><code>ImapClient</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapClient.html#ImapClient(java.lang.String,%20java.lang.String,%20com.aspose.email.ITokenProvider)">ImapClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapClient.html" title="class in com.aspose.email"><code>ImapClient</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapClient.html#ImapClient(java.lang.String,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">ImapClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
          int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapClient.html" title="class in com.aspose.email"><code>ImapClient</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/OAuthNetworkCredential.html#OAuthNetworkCredential(com.aspose.email.ITokenProvider)">OAuthNetworkCredential</a></strong>(<a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/OAuthNetworkCredential.html" title="class in com.aspose.email"><code>OAuthNetworkCredential</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/OAuthNetworkCredential.html#OAuthNetworkCredential(java.lang.String,%20com.aspose.email.ITokenProvider)">OAuthNetworkCredential</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;userName,
                      <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/OAuthNetworkCredential.html" title="class in com.aspose.email"><code>OAuthNetworkCredential</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#Pop3Client(java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">Pop3Client</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          int&nbsp;port,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
          int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/Pop3Client.html" title="class in com.aspose.email"><code>Pop3Client</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#SmtpClient(java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider)">SmtpClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          int&nbsp;port,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpClient.html" title="class in com.aspose.email"><code>SmtpClient</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#SmtpClient(java.lang.String,%20int,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">SmtpClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          int&nbsp;port,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
          int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpClient.html" title="class in com.aspose.email"><code>SmtpClient</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#SmtpClient(java.lang.String,%20java.lang.String,%20com.aspose.email.ITokenProvider)">SmtpClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpClient.html" title="class in com.aspose.email"><code>SmtpClient</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#SmtpClient(java.lang.String,%20java.lang.String,%20com.aspose.email.ITokenProvider,%20int)">SmtpClient</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
          <a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">ITokenProvider</a>&nbsp;tokenProvider,
          int&nbsp;securityOptions)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpClient.html" title="class in com.aspose.email"><code>SmtpClient</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ITokenProvider.html" title="interface in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ITokenProvider.html" target="_top">Frames</a></li>
<li><a href="ITokenProvider.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
