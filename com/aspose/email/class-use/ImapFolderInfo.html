<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ImapFolderInfo (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ImapFolderInfo (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapFolderInfo.html" target="_top">Frames</a></li>
<li><a href="ImapFolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ImapFolderInfo" class="title">Uses of Class<br>com.aspose.email.ImapFolderInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endGetFolderInfo(com.aspose.ms.System.IAsyncResult)">endGetFolderInfo</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the pending asynchronous folder listing to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endSelectFolder(com.aspose.ms.System.IAsyncResult)">endSelectFolder</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the pending asynchronous message fetching to complete.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getAllMessages()">getAllMessages</a></strong>()</code>
<div class="block">
 Gets mailbox presents all messages in the user's message store.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getArchivedMessages()">getArchivedMessages</a></strong>()</code>
<div class="block">
 Gets mailbox is used to archive messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#getCurrentFolder()">getCurrentFolder</a></strong>()</code>
<div class="block">
 Gets the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getDraftMessages()">getDraftMessages</a></strong>()</code>
<div class="block">
 Gets mailbox is used to hold draft messages typically, messages that are being composed but have not yet been sent.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getFlaggedMessages()">getFlaggedMessages</a></strong>()</code>
<div class="block">
 Gets mailbox presents all messages marked in some way as "important".</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#getFolderInfo(com.aspose.email.IConnection,%20java.lang.String)">getFolderInfo</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Returns information about the specified folder without selecting it</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#getFolderInfo(java.lang.String)">getFolderInfo</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName)</code>
<div class="block">
 Returns information about the specified folder without selecting it</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getImportant()">getImportant</a></strong>()</code>
<div class="block">
 Gets mailbox is used to hold messages that have been marked as important.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getInbox()">getInbox</a></strong>()</code>
<div class="block">
 Gets mailbox is used to hold incoming messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getJunkMessages()">getJunkMessages</a></strong>()</code>
<div class="block">
 Gets mailbox is where messages deemed to be junk mail are held.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getSentMessages()">getSentMessages</a></strong>()</code>
<div class="block">
 Gets mailbox is used to hold copies of messages that have been sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#getTrash()">getTrash</a></strong>()</code>
<div class="block">
 Gets mailbox is used to hold messages that have been deleted or marked for deletion.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>[]</code></td>
<td class="colLast"><span class="strong">ImapMailboxInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapMailboxInfo.html#toArray()">toArray</a></strong>()</code>
<div class="block">
 Gets array of existed well-known folders.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapFolderInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html#add(com.aspose.email.ImapFolderInfo)">add</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>&nbsp;item)</code>
<div class="block">
 Adds the ImapFolderInfo to the ImapFolderInfoCollection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endExistFolder(com.aspose.ms.System.IAsyncResult,%20com.aspose.email.ImapFolderInfo[])">endExistFolder</a></strong>(IAsyncResult&nbsp;asyncResult,
              <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>[]&nbsp;folderInfo)</code>
<div class="block">
 Waits for the pending asynchronous operation to complete.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#existFolder(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.ImapFolderInfo[])">existFolder</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
           <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>[]&nbsp;folderInfo)</code>
<div class="block">
 Check whether this folder exists, extract folder info if so</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#existFolder(java.lang.String,%20com.aspose.email.ImapFolderInfo[])">existFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
           <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>[]&nbsp;folderInfo)</code>
<div class="block">
 Check whether this folder exists, extract folder info if so</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#setCurrentFolder(com.aspose.email.ImapFolderInfo)">setCurrentFolder</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>&nbsp;value)</code>
<div class="block">
 Gets the current folder</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapFolderInfoCollection.</span><code><strong><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html#addRange(com.aspose.ms.System.Collections.Generic.IGenericEnumerable)">addRange</a></strong>(IGenericEnumerable&lt;<a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>&gt;&nbsp;folders)</code>
<div class="block">
 Adds the ImapFolderInfo to the ImapFolderInfoCollection.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapFolderInfoCollection.html#ImapFolderInfoCollection(com.aspose.email.ImapFolderInfo...)">ImapFolderInfoCollection</a></strong>(<a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>...&nbsp;folders)</code>
<div class="block">
 Initializes a new instance of the ImapFolderInfoCollection class.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapFolderInfoCollectionBase.html#ImapFolderInfoCollectionBase(com.aspose.ms.System.Collections.Generic.IGenericList)">ImapFolderInfoCollectionBase</a></strong>(IGenericList&lt;<a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">ImapFolderInfo</a>&gt;&nbsp;list)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;</div>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ImapFolderInfoCollectionBase.html" title="class in com.aspose.email"><code>ImapFolderInfoCollectionBase</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ImapFolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ImapFolderInfo.html" target="_top">Frames</a></li>
<li><a href="ImapFolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
