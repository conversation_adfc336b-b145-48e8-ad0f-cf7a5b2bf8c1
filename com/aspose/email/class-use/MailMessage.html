<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MailMessage (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MailMessage (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailMessage.html" target="_top">Frames</a></li>
<li><a href="MailMessage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MailMessage" class="title">Uses of Class<br>com.aspose.email.MailMessage</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AmpMessage.html" title="class in com.aspose.email">AmpMessage</a></strong></code>
<div class="block">
 Message which allows senders to include AMP components inside emails.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type parameters of type <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesResult.html#failed">failed</a></strong></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesResult.html#notHandled">notHandled</a></strong></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesResult.html#succeeded">succeeded</a></strong></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#attachDetachedSignature(com.aspose.email.SmimeKey)">attachDetachedSignature</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a signed message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#attachSignature(byte[],%20java.lang.String)">attachSignature</a></strong>(byte[]&nbsp;certificateRawData,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;certificatePassword)</code>
<div class="block">
 Creates a signed message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#attachSignature(byte[],%20java.lang.String,%20boolean)">attachSignature</a></strong>(byte[]&nbsp;certificateRawData,
               <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;certificatePassword,
               boolean&nbsp;detached)</code>
<div class="block">
 Creates a signed message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MailMessage,%20com.aspose.email.SmimeKey)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a copy of the specified MailMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MailMessage,%20com.aspose.email.SmimeKey,%20com.aspose.email.SignatureOptions)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key,
               <a href="../../../../com/aspose/email/SignatureOptions.html" title="class in com.aspose.email">SignatureOptions</a>&nbsp;options)</code>
<div class="block">
 Creates a copy of the specified MailMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#attachSignature(com.aspose.email.SmimeKey)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a signed message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#attachSignature(com.aspose.email.SmimeKey,%20boolean)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key,
               boolean&nbsp;detached)</code>
<div class="block">
 Creates a signed message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ForwardMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ForwardMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ReplyMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ReplyMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the replying messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding and replying messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createMessage(java.lang.String,%20com.aspose.email.MailMessage)">createMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderId,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Creates message in specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#createReadReceipt(java.lang.String,%20java.lang.String)">createReadReceipt</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;bodyText)</code>
<div class="block">
 Creates the read receipt.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#decrypt()">decrypt</a></strong>()</code>
<div class="block">
 Decrypts this message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#decrypt(byte[],%20java.lang.String)">decrypt</a></strong>(byte[]&nbsp;certificateRawData,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;certificatePassword)</code>
<div class="block">
 Decrypts this message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#decrypt(com.aspose.email.SmimeKey)">decrypt</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Decrypts this message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#deepClone()">deepClone</a></strong>()</code>
<div class="block">
 Clones this instance</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#dKIMSign(java.security.PrivateKey,%20com.aspose.email.DKIMSignatureInfo)">dKIMSign</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/security/PrivateKey.html?is-external=true" title="class or interface in java.security">PrivateKey</a>&nbsp;rsa,
        <a href="../../../../com/aspose/email/DKIMSignatureInfo.html" title="class in com.aspose.email">DKIMSignatureInfo</a>&nbsp;signatureInfo)</code>
<div class="block">
 Signs this message using DKIM (DomainKeys Identified Mail) signature.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#encrypt(byte[],%20java.lang.String)">encrypt</a></strong>(byte[]&nbsp;certificateRawData,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;certificatePassword)</code>
<div class="block">
 Encrypts this message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#encrypt(com.aspose.email.SmimeKey)">encrypt</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Encrypts this message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endFetchMessage(com.aspose.ms.System.IAsyncResult)">endFetchMessage</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the pending asynchronous message fetching to complete.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#endFetchMessage(com.aspose.ms.System.IAsyncResult)">endFetchMessage</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous operation to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#extractMessage(java.lang.String,%20com.aspose.email.EmlLoadOptions)">extractMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
              <a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">Get the message from MBOX.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(com.aspose.email.IConnection,%20int)">fetchMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            int&nbsp;sequenceNumber)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessage(com.aspose.email.IConnection,%20int)">fetchMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            int&nbsp;sequenceNumber)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(com.aspose.email.IConnection,%20int,%20boolean)">fetchMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            int&nbsp;sequenceNumber,
            boolean&nbsp;ignoreAttachment)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(com.aspose.email.IConnection,%20java.lang.String)">fetchMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessage(com.aspose.email.IConnection,%20java.lang.String)">fetchMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(int)">fetchMessage</a></strong>(int&nbsp;sequenceNumber)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessage(int)">fetchMessage</a></strong>(int&nbsp;sequenceNumber)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(int,%20boolean)">fetchMessage</a></strong>(int&nbsp;sequenceNumber,
            boolean&nbsp;ignoreAttachment)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#fetchMessage(java.lang.String)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageUri)</code>
<div class="block">
 Fetches the mail message with specified uri.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMessage(java.lang.String)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageUri)</code>
<div class="block">
 Fetches the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessage(java.lang.String)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessage(java.lang.String)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uniqueId)</code>
<div class="block">
 Fetches the message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMessage(java.lang.String,%20java.lang.Iterable)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Fetches the message from server</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">TgzReader.</span><code><strong><a href="../../../../com/aspose/email/TgzReader.html#getCurrentMessage()">getCurrentMessage</a></strong>()</code>
<div class="block">
 Gets the current message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MimeItemCopyEventArgs.</span><code><strong><a href="../../../../com/aspose/email/MimeItemCopyEventArgs.html#getItem()">getItem</a></strong>()</code>
<div class="block">
 Gets the encapsulated MailMessage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessageEventArgs.</span><code><strong><a href="../../../../com/aspose/email/MailMessageEventArgs.html#getMessage()">getMessage</a></strong>()</code>
<div class="block">
 Gets the sending message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">BounceResult.</span><code><strong><a href="../../../../com/aspose/email/BounceResult.html#getOriginalMessage()">getOriginalMessage</a></strong>()</code>
<div class="block">
 Contains the original message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#load(java.io.InputStream)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Load message from stream</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#load(java.io.InputStream,%20com.aspose.email.LoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
    <a href="../../../../com/aspose/email/LoadOptions.html" title="class in com.aspose.email">LoadOptions</a>&nbsp;options)</code>
<div class="block">
 Load message from stream with additional options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#load(java.lang.String)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">
 Load message from file</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#load(java.lang.String,%20com.aspose.email.LoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
    <a href="../../../../com/aspose/email/LoadOptions.html" title="class in com.aspose.email">LoadOptions</a>&nbsp;options)</code>
<div class="block">
 Load message from file with additional options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">TemplateEngine.</span><code><strong><a href="../../../../com/aspose/email/TemplateEngine.html#merge(com.aspose.email.DataRow)">merge</a></strong>(<a href="../../../../com/aspose/email/DataRow.html" title="class in com.aspose.email">DataRow</a>&nbsp;row)</code>
<div class="block">
 Merge a source DataRow with the template.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">TemplateEngine.</span><code><strong><a href="../../../../com/aspose/email/TemplateEngine.html#merge(com.aspose.email.MailMessage)">merge</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;source)</code>
<div class="block">
 Merge a source MailMessage with the template</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxoStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxoStorageReader.html#readNextMessage()">readNextMessage</a></strong>()</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageReader.html#readNextMessage()">readNextMessage</a></strong>()</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#readNextMessage()">readNextMessage</a></strong>()</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxoStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxoStorageReader.html#readNextMessage(com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageReader.html#readNextMessage(com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#readNextMessage(com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxoStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxoStorageReader.html#readNextMessage(java.lang.String[])">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker)</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageReader.html#readNextMessage(java.lang.String[])">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker)</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#readNextMessage(java.lang.String[])">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker)</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxoStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxoStorageReader.html#readNextMessage(java.lang.String[],%20com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker,
               <a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageReader.html#readNextMessage(java.lang.String[],%20com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker,
               <a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#readNextMessage(java.lang.String[],%20com.aspose.email.EmlLoadOptions)">readNextMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker,
               <a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">
 Reads the next message from underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#removeSignature()">removeSignature</a></strong>()</code>
<div class="block">
 Remove signature</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#toMailMessage()">toMailMessage</a></strong>()</code>
<div class="block">
 Converts ICalendar item (.ics) to MIME (.eml) message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#toMailMessage(int)">toMailMessage</a></strong>(int&nbsp;action)</code>
<div class="block">
 Converts ICalendar item (.ics) to MIME (.eml) message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#toMailMessage(int,%20int)">toMailMessage</a></strong>(int&nbsp;action,
             int&nbsp;seqId)</code>
<div class="block">
 Converts ICalendar item (.ics) to MIME (.eml) message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#toMailMessage(com.aspose.email.MailConversionOptions)">toMailMessage</a></strong>(<a href="../../../../com/aspose/email/MailConversionOptions.html" title="class in com.aspose.email">MailConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Creates an instance of MailMessage from this MapiMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MailMessage)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MailMessage,%20com.aspose.email.UpdateSettings)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
             <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#endFetchMessages(com.aspose.ms.System.IAsyncResult)">endFetchMessages</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous operation to complete.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#endFetchMessages(com.aspose.ms.System.IAsyncResult)">endFetchMessages</a></strong>(IAsyncResult&nbsp;asyncResult)</code>
<div class="block">
 Waits for the asynchronous operation to complete.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages()">enumerateMessages</a></strong>()</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in storage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">NotesStorageFacility.</span><code><strong><a href="../../../../com/aspose/email/NotesStorageFacility.html#enumerateMessages()">enumerateMessages</a></strong>()</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.EmlLoadOptions)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options)</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in storage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.EmlLoadOptions,%20int,%20int)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options,
                 int&nbsp;startIndex,
                 int&nbsp;count)</code>
<div class="block">
 Enumerates a specified number of mail messages, starting from the given index, using the provided load options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.EmlLoadOptions,%20com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Enumerates the mail messages that match the specified query, using the provided load options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(int,%20int)">enumerateMessages</a></strong>(int&nbsp;startIndex,
                 int&nbsp;count)</code>
<div class="block">
 Enumerates a specified number of mail messages, starting from the given index.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Enumerates the mail messages that match the specified query.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessagesBySequences(com.aspose.email.IConnection,%20java.lang.Iterable)">fetchMessagesBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceNumbers)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessagesBySequences(com.aspose.email.IConnection,%20java.lang.Iterable)">fetchMessagesBySequences</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceNumbers)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessagesBySequences(java.lang.Iterable)">fetchMessagesBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceNumbers)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessagesBySequences(java.lang.Iterable)">fetchMessagesBySequences</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;sequenceNumbers)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessagesByUids(com.aspose.email.IConnection,%20java.lang.Iterable)">fetchMessagesByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uids)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessagesByUids(com.aspose.email.IConnection,%20java.lang.Iterable)">fetchMessagesByUids</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uids)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#fetchMessagesByUids(java.lang.Iterable)">fetchMessagesByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uids)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#fetchMessagesByUids(java.lang.Iterable)">fetchMessagesByUids</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uids)</code>
<div class="block">
 Fetches the messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/RuntimeException.html?is-external=true" title="class or interface in java.lang">RuntimeException</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesFromMessageObjectResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesFromMessageObjectResult.html#getFailed()">getFailed</a></strong>()</code>
<div class="block">
 Gets mail messages that have been handled with errors</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>Dictionary&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&gt;</code></td>
<td class="colLast"><span class="strong">SendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/SendMessagesResult.html#getFailed()">getFailed</a></strong>()</code>
<div class="block">
 Gets mail messages that have been handled with errors</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesFromMessageObjectResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesFromMessageObjectResult.html#getNotHandled()">getNotHandled</a></strong>()</code>
<div class="block">
 Gets mail messages that have not been handled</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">SendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/SendMessagesResult.html#getNotHandled()">getNotHandled</a></strong>()</code>
<div class="block">
 Gets mail messages that have not been handled</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><span class="strong">AppendMessagesFromMessageObjectResult.</span><code><strong><a href="../../../../com/aspose/email/AppendMessagesFromMessageObjectResult.html#getSucceeded()">getSucceeded</a></strong>()</code>
<div class="block">
 Gets mail messages that have been handled successfully</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">SendMessagesResult.</span><code><strong><a href="../../../../com/aspose/email/SendMessagesResult.html#getSucceeded()">getSucceeded</a></strong>()</code>
<div class="block">
 Gets mail messages that have been handled successfully</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerator&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#iterator()">iterator</a></strong>()</code>
<div class="block">
 Returns an enumerator that iterates through a collection.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessage(com.aspose.email.IConnection,%20com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the current folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessage(com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the Inbox folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessage(com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the current folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#appendMessage(java.lang.String,%20com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessage(java.lang.String,%20com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessage(java.lang.String,%20com.aspose.email.MailMessage)">appendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#appendMessage(java.lang.String,%20com.aspose.email.MailMessage,%20boolean)">appendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
             boolean&nbsp;markAsSent)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessages(com.aspose.email.MailMessage...)">appendMessages</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessages(java.lang.String,%20com.aspose.email.MailMessage...)">appendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
              <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MailMessage,%20com.aspose.email.SmimeKey)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a copy of the specified MailMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MailMessage,%20com.aspose.email.SmimeKey,%20com.aspose.email.SignatureOptions)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key,
               <a href="../../../../com/aspose/email/SignatureOptions.html" title="class in com.aspose.email">SignatureOptions</a>&nbsp;options)</code>
<div class="block">
 Creates a copy of the specified MailMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(com.aspose.email.IConnection,%20com.aspose.email.MailMessage)">beginAppendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins append message to the end of the specified folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailMessage)">beginAppendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginAppendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAppendMessage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
                  AsyncCallback&nbsp;callback,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(com.aspose.email.MailMessage)">beginAppendMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins append message to the end of the specified folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(java.lang.String,%20com.aspose.email.MailMessage)">beginAppendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginAppendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
                  AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessage(java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAppendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
                  AsyncCallback&nbsp;callback,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginForward(java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginForward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
            <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins forward email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">MailStorageConverter.MailHandler.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html#beginInvoke(com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginInvoke</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20com.aspose.email.MailMessage...)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20com.aspose.email.MailMessage)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.MailMessage...)">beginSend</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.MailMessage)">beginSend</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.MailMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ForwardMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ForwardMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ReplyMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ReplyMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the replying messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#buildResponse(com.aspose.email.MailMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding and replying messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/SmimeResult.html" title="class in com.aspose.email">SmimeResult</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#checkSignature(com.aspose.email.MailMessage)">checkSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>
<div class="block">
 Checking signature MailMessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/SmimeResult.html" title="class in com.aspose.email">SmimeResult</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#checkSignature(com.aspose.email.MailMessage,%20com.aspose.email.SmimeKey)">checkSignature</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg,
              <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Checking signature MailMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createMessage(java.lang.String,%20com.aspose.email.MailMessage)">createMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderId,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Creates message in specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IMessageFormatter.</span><code><strong><a href="../../../../com/aspose/email/IMessageFormatter.html#format(com.aspose.email.MailMessage)">format</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Formats the specified message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#forward(com.aspose.email.MailMessage,%20com.aspose.email.ExchangeMessageInfo)">forward</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
       <a href="../../../../com/aspose/email/ExchangeMessageInfo.html" title="class in com.aspose.email">ExchangeMessageInfo</a>&nbsp;referencedMessage)</code>
<div class="block">
 Forward a message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#forward(com.aspose.email.MailMessage,%20java.lang.String)">forward</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;referencedUri)</code>
<div class="block">
 Forward a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(java.lang.String,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;recipients,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#forward(java.lang.String,%20java.lang.String,%20com.aspose.email.MailMessage)">forward</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;sender,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient,
       <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Forwards specified message to recipient</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(com.aspose.email.MailMessage)">fromMailMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Creates an instance of MapiMessage from the MailMessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(com.aspose.email.MailMessage,%20com.aspose.email.MapiConversionOptions)">fromMailMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
               <a href="../../../../com/aspose/email/MapiConversionOptions.html" title="class in com.aspose.email">MapiConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Creates an instance of MapiMessage from the MailMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">MailMessage.</span><code><strong><a href="../../../../com/aspose/email/MailMessage.html#getHtmlWithResources(java.lang.String,%20com.aspose.email.MailMessage)">getHtmlWithResources</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;html,
                    <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;msg)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="strong">MailStorageConverter.MailHandler.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html#invoke(com.aspose.email.MailMessage)">invoke</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">TemplateEngine.</span><code><strong><a href="../../../../com/aspose/email/TemplateEngine.html#merge(com.aspose.email.MailMessage)">merge</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;source)</code>
<div class="block">
 Merge a source MailMessage with the template</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#reply(com.aspose.email.MailMessage,%20com.aspose.email.ExchangeMessageInfo)">reply</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
     <a href="../../../../com/aspose/email/ExchangeMessageInfo.html" title="class in com.aspose.email">ExchangeMessageInfo</a>&nbsp;referencedMessage)</code>
<div class="block">
 Reply to the sender's message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#reply(com.aspose.email.MailMessage,%20java.lang.String)">reply</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
     <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;referencedUri)</code>
<div class="block">
 Reply to the sender's message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#replyAll(com.aspose.email.MailMessage,%20com.aspose.email.ExchangeMessageInfo)">replyAll</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
        <a href="../../../../com/aspose/email/ExchangeMessageInfo.html" title="class in com.aspose.email">ExchangeMessageInfo</a>&nbsp;referencedMessage)</code>
<div class="block">
 Reply to the sender and all recipients of a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(com.aspose.email.IConnection,%20com.aspose.email.MailMessage...)">send</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
    <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Send the specified message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(com.aspose.email.IConnection,%20com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
    <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Send the specified message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(com.aspose.email.MailMessage...)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>...&nbsp;messages)</code>
<div class="block">
 Send the specified message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends the mail message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">IDeliveryServiceClient.</span><code><strong><a href="../../../../com/aspose/email/IDeliveryServiceClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends the specified message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email message using MIME format</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IMailTransferAgent.</span><code><strong><a href="../../../../com/aspose/email/IMailTransferAgent.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Send an Email message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">MailgunClient.</span><code><strong><a href="../../../../com/aspose/email/MailgunClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">SendGridClient.</span><code><strong><a href="../../../../com/aspose/email/SendGridClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(com.aspose.email.MailMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Send the specified message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#send(com.aspose.email.MailMessage,%20com.aspose.email.FollowUpOptions)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
    <a href="../../../../com/aspose/email/FollowUpOptions.html" title="class in com.aspose.email">FollowUpOptions</a>&nbsp;messageOptions)</code>
<div class="block">
 Sends the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">IDeliveryServiceClient.</span><code><strong><a href="../../../../com/aspose/email/IDeliveryServiceClient.html#send(com.aspose.email.MailMessage,%20java.util.List,%20com.aspose.email.CancellationToken)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;tags,
    com.aspose.email.CancellationToken&nbsp;token)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">MailgunClient.</span><code><strong><a href="../../../../com/aspose/email/MailgunClient.html#send(com.aspose.email.MailMessage,%20java.util.List,%20com.aspose.email.CancellationToken)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;tags,
    com.aspose.email.CancellationToken&nbsp;token)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/DeliveryServiceResponse.html" title="class in com.aspose.email">DeliveryServiceResponse</a></code></td>
<td class="colLast"><span class="strong">SendGridClient.</span><code><strong><a href="../../../../com/aspose/email/SendGridClient.html#send(com.aspose.email.MailMessage,%20java.util.List,%20com.aspose.email.CancellationToken)">send</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;tags,
    com.aspose.email.CancellationToken&nbsp;token)</code>
<div class="block">
 Sends email synchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><span class="strong">SpamAnalyzer.</span><code><strong><a href="../../../../com/aspose/email/SpamAnalyzer.html#test(com.aspose.email.MailMessage)">test</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Analyses the message and returns the probability of the message being spam.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SpamAnalyzer.</span><code><strong><a href="../../../../com/aspose/email/SpamAnalyzer.html#trainFilter(com.aspose.email.MailMessage[],%20com.aspose.email.MailMessage[])">trainFilter</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>[]&nbsp;ham,
           <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>[]&nbsp;spam)</code>
<div class="block">
 Learns from the specified messages as from spam or non-spam source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SpamAnalyzer.</span><code><strong><a href="../../../../com/aspose/email/SpamAnalyzer.html#trainFilter(com.aspose.email.MailMessage[],%20com.aspose.email.MailMessage[])">trainFilter</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>[]&nbsp;ham,
           <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>[]&nbsp;spam)</code>
<div class="block">
 Learns from the specified messages as from spam or non-spam source.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SpamAnalyzer.</span><code><strong><a href="../../../../com/aspose/email/SpamAnalyzer.html#trainFilter(com.aspose.email.MailMessage,%20boolean)">trainFilter</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
           boolean&nbsp;isSpam)</code>
<div class="block">
 Learns from the specified message as from spam or non-spam source.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MailMessage)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MailMessage,%20com.aspose.email.UpdateSettings)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
             <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageWriter.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageWriter.html#writeMessage(com.aspose.email.MailMessage)">writeMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Writes the message to underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">MboxStorageWriter.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageWriter.html#writeMessage(com.aspose.email.MailMessage)">writeMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Writes the message to underlying storage stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">MboxrdStorageWriter.</span><code><strong><a href="../../../../com/aspose/email/MboxrdStorageWriter.html#writeMessage(com.aspose.email.MailMessage,%20java.lang.String[])">writeMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker)</code>
<div class="block">
 Writes the message to underlying storage stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">MboxStorageWriter.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageWriter.html#writeMessage(com.aspose.email.MailMessage,%20java.lang.String[])">writeMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]&nbsp;fromMarker)</code>
<div class="block">
 Writes the message to underlying storage stream.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppendMessagesResult.html" title="class in com.aspose.email">AppendMessagesResult</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessages(com.aspose.email.IConnection,%20java.lang.Iterable)">appendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the current folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppendMessagesResult.html" title="class in com.aspose.email">AppendMessagesResult</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessages(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.Iterable)">appendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessages(java.lang.Iterable)">appendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppendMessagesResult.html" title="class in com.aspose.email">AppendMessagesResult</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessages(java.lang.Iterable)">appendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the current folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessages(java.lang.String,%20java.lang.Iterable)">appendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppendMessagesResult.html" title="class in com.aspose.email">AppendMessagesResult</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#appendMessages(java.lang.String,%20java.lang.Iterable)">appendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
              <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(com.aspose.email.IConnection,%20java.lang.Iterable)">beginAppendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins append message to the end of the specified folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.Iterable)">beginAppendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginAppendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(com.aspose.email.IConnection,%20java.lang.String,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAppendMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(java.lang.Iterable)">beginAppendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins append message to the end of the specified folder
 If current folder hasn't been specified default folder is used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(java.lang.String,%20java.lang.Iterable)">beginAppendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(java.lang.String,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginAppendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
                   AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginAppendMessages(java.lang.String,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginAppendMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
                   AsyncCallback&nbsp;callback,
                   <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins append message to the end of the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.EventHandler)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.EventHandler,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(com.aspose.email.IConnection,%20java.lang.Iterable,%20com.aspose.ms.System.EventHandler,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable,%20com.aspose.ms.System.EventHandler)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable,%20com.aspose.ms.System.EventHandler,%20com.aspose.ms.System.AsyncCallback)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate,
         AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#beginSend(java.lang.Iterable,%20com.aspose.ms.System.EventHandler,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginSend</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages,
         EventHandler&lt;<a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email">MailMessageEventArgs</a>&gt;&nbsp;messageSentDelegate,
         AsyncCallback&nbsp;callback,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins sending email asynchronously.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(com.aspose.email.IConnection,%20java.lang.Iterable)">send</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Send the specified messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#send(java.lang.Iterable)">send</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Send the specified messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">SmtpClient.</span><code><strong><a href="../../../../com/aspose/email/SmtpClient.html#sendToQueue(java.lang.Iterable)">sendToQueue</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Append messages to queue</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/FailedMailMessageEventArgs.html#FailedMailMessageEventArgs(com.aspose.email.MailMessage,%20java.lang.Throwable)">FailedMailMessageEventArgs</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
                          <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;ex)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/FailedMailMessageEventArgs.html" title="class in com.aspose.email"><code>FailedMailMessageEventArgs</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MailMessageEventArgs.html#MailMessageEventArgs(com.aspose.email.MailMessage)">MailMessageEventArgs</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MailMessageEventArgs.html" title="class in com.aspose.email"><code>MailMessageEventArgs</code></a> class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpException.html#SmtpException(java.lang.String,%20com.aspose.email.MailMessage)">SmtpException</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;mailMessage)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpException.html" title="class in com.aspose.email"><code>SmtpException</code></a> class</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/SmtpException.html#SmtpException(java.lang.String,%20java.lang.Throwable,%20com.aspose.email.MailMessage)">SmtpException</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
             <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;innerException,
             <a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;mailMessage)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/SmtpException.html" title="class in com.aspose.email"><code>SmtpException</code></a> class</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/TemplateEngine.html#TemplateEngine(com.aspose.email.MailMessage)">TemplateEngine</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;templateMessage)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/TemplateEngine.html" title="class in com.aspose.email"><code>TemplateEngine</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailMessage.html" target="_top">Frames</a></li>
<li><a href="MailMessage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
