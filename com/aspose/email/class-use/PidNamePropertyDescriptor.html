<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.PidNamePropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.PidNamePropertyDescriptor (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PidNamePropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PidNamePropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.PidNamePropertyDescriptor" class="title">Uses of Class<br>com.aspose.email.PidNamePropertyDescriptor</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ACCEPT_LANGUAGE">ACCEPT_LANGUAGE</a></strong></code>
<div class="block">
 Contains the value of the MIME Accept-Language header.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#APPLICATION_NAME">APPLICATION_NAME</a></strong></code>
<div class="block">
 Specifies the application used to open the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_IS_FOLDER">ATTACHMENT_IS_FOLDER</a></strong></code>
<div class="block">
 A value indicating whether the attachment points to a folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_MAC_CONTENT_TYPE">ATTACHMENT_MAC_CONTENT_TYPE</a></strong></code>
<div class="block">
 Contains the Content-Type of the Mac attachment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_MAC_INFO">ATTACHMENT_MAC_INFO</a></strong></code>
<div class="block">
 Contains the headers and resource fork data associated with the Mac attachment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_ORIGINAL_PERMISSION_TYPE">ATTACHMENT_ORIGINAL_PERMISSION_TYPE</a></strong></code>
<div class="block">
 The original permission of the attachment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_ORIGINAL_URL">ATTACHMENT_ORIGINAL_URL</a></strong></code>
<div class="block">
 The original URL of the attachment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_PERMISSION_TYPE">ATTACHMENT_PERMISSION_TYPE</a></strong></code>
<div class="block">
 The permission of the attachment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_PREVIEW_URL">ATTACHMENT_PREVIEW_URL</a></strong></code>
<div class="block">
 The URL of the attachment preview.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_PROVIDER_ENDPOINT_URL">ATTACHMENT_PROVIDER_ENDPOINT_URL</a></strong></code>
<div class="block">
 The URL of the attachment provider.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_PROVIDER_TYPE">ATTACHMENT_PROVIDER_TYPE</a></strong></code>
<div class="block">
 The type of the attachment provider.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#ATTACHMENT_THUMBNAIL_URL">ATTACHMENT_THUMBNAIL_URL</a></strong></code>
<div class="block">
 The URL of the attachment thumbnail.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUDIO_NOTES">AUDIO_NOTES</a></strong></code>
<div class="block">
 Contains textual annotations to a voice message after it has been delivered to the user's mailbox.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTHOR">AUTHOR</a></strong></code>
<div class="block">
 Specifies the author of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#AUTOMATIC_SPEECH_RECOGNITION_DATA">AUTOMATIC_SPEECH_RECOGNITION_DATA</a></strong></code>
<div class="block">
 Contains an unprotected voice message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#BYTE_COUNT">BYTE_COUNT</a></strong></code>
<div class="block">
 Specifies the size, in bytes, of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_ATTENDEE_ROLE">CALENDAR_ATTENDEE_ROLE</a></strong></code>
<div class="block">
 Specifies the role of the attendee.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_BUSYSTATUS">CALENDAR_BUSYSTATUS</a></strong></code>
<div class="block">
 Specifies whether the attendee is busy at the time of an appointment on their calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_CONTACT">CALENDAR_CONTACT</a></strong></code>
<div class="block">
 Identifies the name of a contact who is an attendee of a meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_CONTACT_URL">CALENDAR_CONTACT_URL</a></strong></code>
<div class="block">
 Identifies the URL where you can access contact information in HTML format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_CREATED">CALENDAR_CREATED</a></strong></code>
<div class="block">
 Identifies the date and time, in UTC, when the organizer created the appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_DESCRIPTION_URL">CALENDAR_DESCRIPTION_URL</a></strong></code>
<div class="block">
 Specifies the URL of a resource that contains a description of an appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_DURATION">CALENDAR_DURATION</a></strong></code>
<div class="block">
 Identifies the duration, in seconds, of an appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_EXCEPTION_DATE">CALENDAR_EXCEPTION_DATE</a></strong></code>
<div class="block">
 Identifies a list of dates that are exceptions to a recurring appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_EXCEPTION_RULE">CALENDAR_EXCEPTION_RULE</a></strong></code>
<div class="block">
 Specifies an exception rule for a recurring appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_GEO_LATITUDE">CALENDAR_GEO_LATITUDE</a></strong></code>
<div class="block">
 Specifies the geographical latitude of the location of an appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_GEO_LONGITUDE">CALENDAR_GEO_LONGITUDE</a></strong></code>
<div class="block">
 Specifies the geographical longitude of the location of an appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_INSTANCE_TYPE">CALENDAR_INSTANCE_TYPE</a></strong></code>
<div class="block">
 Specifies the type of an appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_IS_ORGANIZER">CALENDAR_IS_ORGANIZER</a></strong></code>
<div class="block">
 Specifies whether an attendee is the organizer of an appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_LAST_MODIFIED">CALENDAR_LAST_MODIFIED</a></strong></code>
<div class="block">
 Specifies the date and time, in UTC, when an appointment was last modified.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_LOCATION_URL">CALENDAR_LOCATION_URL</a></strong></code>
<div class="block">
 Specifies a URL with location information in HTML format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_MEETING_STATUS">CALENDAR_MEETING_STATUS</a></strong></code>
<div class="block">
 Specifies the status of an appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_METHOD">CALENDAR_METHOD</a></strong></code>
<div class="block">
 Specifies the iCalendar method that is associated with an Appointment object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_PRODUCT_ID">CALENDAR_PRODUCT_ID</a></strong></code>
<div class="block">
 Identifies the product that created the iCalendar-formatted stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_RECURRENCE_ID_RANGE">CALENDAR_RECURRENCE_ID_RANGE</a></strong></code>
<div class="block">
 Specifies which instances of a recurring appointment are being referred to.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_REMINDER_OFFSET">CALENDAR_REMINDER_OFFSET</a></strong></code>
<div class="block">
 Identifies the number of seconds before an appointment starts that a reminder is to be displayed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_RESOURCES">CALENDAR_RESOURCES</a></strong></code>
<div class="block">
 Identifies a list of resources, such as rooms and video equipment, that are available for an appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_RSVP">CALENDAR_RSVP</a></strong></code>
<div class="block">
 Specifies whether the organizer of an appointment or meeting requested a response.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_SEQUENCE">CALENDAR_SEQUENCE</a></strong></code>
<div class="block">
 Specifies the sequence number of a version of an appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_TIME_ZONE">CALENDAR_TIME_ZONE</a></strong></code>
<div class="block">
 Specifies the time zone of an appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_TIME_ZONE_ID">CALENDAR_TIME_ZONE_ID</a></strong></code>
<div class="block">
 Specifies the time zone identifier of an appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_TRANSPARENT">CALENDAR_TRANSPARENT</a></strong></code>
<div class="block">
 Specifies whether an appointment or meeting is visible to busy time searches.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_UID">CALENDAR_UID</a></strong></code>
<div class="block">
 Specifies the unique identifier of an appointment or meeting.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CALENDAR_VERSION">CALENDAR_VERSION</a></strong></code>
<div class="block">
 Identifies the version of the iCalendar specification, as specified in [MS-OXCICAL] section *******.1.3, that is required to correctly interpret an iCalendar object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CATEGORY">CATEGORY</a></strong></code>
<div class="block">
 Specifies the category of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CHARACTER_COUNT">CHARACTER_COUNT</a></strong></code>
<div class="block">
 Specifies the character count of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COMMENTS">COMMENTS</a></strong></code>
<div class="block">
 Specifies the comments of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#COMPANY">COMPANY</a></strong></code>
<div class="block">
 Specifies the company for which the file was created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTENT_BASE">CONTENT_BASE</a></strong></code>
<div class="block">
 Specifies the value of the MIME Content-Base header, which defines the base URI for resolving relative URLs contained within the message body.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTENT_CLASS">CONTENT_CLASS</a></strong></code>
<div class="block">
 Contains a string that identifies the type of content of a Message object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CONTENT_TYPE">CONTENT_TYPE</a></strong></code>
<div class="block">
 Specifies the type of the body part content.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CREATE_DATE_TIME_READ_ONLY">CREATE_DATE_TIME_READ_ONLY</a></strong></code>
<div class="block">
 Specifies the time, in UTC, that the file was first created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#CROSS_REFERENCE">CROSS_REFERENCE</a></strong></code>
<div class="block">
 Contains the name of the host (with domains omitted) and a white-space-separated list of colon-separated pairs of newsgroup names and message numbers.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAV_ID">DAV_ID</a></strong></code>
<div class="block">
 Specifies a unique ID for the calendar item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAV_IS_COLLECTION">DAV_IS_COLLECTION</a></strong></code>
<div class="block">
 Indicates whether a Calendar object is a collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAV_IS_STRUCTURED_DOCUMENT">DAV_IS_STRUCTURED_DOCUMENT</a></strong></code>
<div class="block">
 Indicates whether a Calendar object is a structured document.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAV_PARENT_NAME">DAV_PARENT_NAME</a></strong></code>
<div class="block">
 Specifies the name of the Folder object that contains the Calendar object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DAV_UID">DAV_UID</a></strong></code>
<div class="block">
 Specifies the unique identifier for an item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#DOCUMENT_PARTS">DOCUMENT_PARTS</a></strong></code>
<div class="block">
 Specifies the title of each part of the document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EDIT_TIME">EDIT_TIME</a></strong></code>
<div class="block">
 Specifies the time that the file was last edited.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCH_DATA_EXPECTED_CONTENT_CLASS">EXCH_DATA_EXPECTED_CONTENT_CLASS</a></strong></code>
<div class="block">
 Specifies an array of names that indicates the expected content classes of items within a folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCH_DATA_SCHEMA_COLLECTION_REFERENCE">EXCH_DATA_SCHEMA_COLLECTION_REFERENCE</a></strong></code>
<div class="block">
 Specifies an array of names that indicates the expected content classes of items within a folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCH_DATABASE_SCHEMA">EXCH_DATABASE_SCHEMA</a></strong></code>
<div class="block">
 Specifies an array of URLs that identifies other folders within the same message store that contain schema definition items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_INTENDED_BUSY_STATUS">EXCHANGE_INTENDED_BUSY_STATUS</a></strong></code>
<div class="block">
 Specifies the intended free/busy status of a meeting in a Meeting request.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_JUNK_EMAIL_MOVE_STAMP">EXCHANGE_JUNK_EMAIL_MOVE_STAMP</a></strong></code>
<div class="block">
 Indicates that the message is not to be processed by a spam filter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_MODIFY_EXCEPTION_STRUCTURE">EXCHANGE_MODIFY_EXCEPTION_STRUCTURE</a></strong></code>
<div class="block">
 Specifies a structure that modifies an exception to the recurrence.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_NO_MODIFY_EXCEPTIONS">EXCHANGE_NO_MODIFY_EXCEPTIONS</a></strong></code>
<div class="block">
 Indicates whether exceptions to a recurring appointment can be modified.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_PATTERN_END">EXCHANGE_PATTERN_END</a></strong></code>
<div class="block">
 Identifies the maximum time when an instance of a recurring appointment ends.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_PATTERN_START">EXCHANGE_PATTERN_START</a></strong></code>
<div class="block">
 Identifies the absolute minimum time when an instance of a recurring appointment starts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXCHANGE_REMINDER_INTERVAL">EXCHANGE_REMINDER_INTERVAL</a></strong></code>
<div class="block">
 Identifies the time, in seconds, between reminders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_ADDRESSES">EXTRACTED_ADDRESSES</a></strong></code>
<div class="block">
 Contains an XML document with a single AddressSet element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_CONTACTS">EXTRACTED_CONTACTS</a></strong></code>
<div class="block">
 Contains an XML document with a single ContactSet element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_EMAILS">EXTRACTED_EMAILS</a></strong></code>
<div class="block">
 Contains an XML document with a single EmailSet element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_MEETINGS">EXTRACTED_MEETINGS</a></strong></code>
<div class="block">
 Contains an XML document with a single MeetingSet element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_PHONES">EXTRACTED_PHONES</a></strong></code>
<div class="block">
 Contains an XML document with a single PhoneSet element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_TASKS">EXTRACTED_TASKS</a></strong></code>
<div class="block">
 Contains an XML document with a single TaskSet element.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#EXTRACTED_URLS">EXTRACTED_URLS</a></strong></code>
<div class="block">
 Contains an XML document with a single UrlSet element.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#FROM">FROM</a></strong></code>
<div class="block">
 Specifies the SMTP email alias of the organizer of an appointment or meeting.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HEADING_PAIRS">HEADING_PAIRS</a></strong></code>
<div class="block">
 Specifies which group of headings are indented in the document.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HIDDEN_COUNT">HIDDEN_COUNT</a></strong></code>
<div class="block">
 Specifies the hidden value of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HTTPMAIL_CALENDAR">HTTPMAIL_CALENDAR</a></strong></code>
<div class="block">
 Specifies the URL for the Calendar folder for a particular user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HTTPMAIL_HTML_DESCRIPTION">HTTPMAIL_HTML_DESCRIPTION</a></strong></code>
<div class="block">
 Specifies the HTML content of the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#HTTPMAIL_SEND_MESSAGE">HTTPMAIL_SEND_MESSAGE</a></strong></code>
<div class="block">
 Specifies the email submission URI to which outgoing email is submitted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#I_CALENDAR_RECURRENCE_DATE">I_CALENDAR_RECURRENCE_DATE</a></strong></code>
<div class="block">
 Identifies an array of instances of a recurring appointment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#I_CALENDAR_RECURRENCE_RULE">I_CALENDAR_RECURRENCE_RULE</a></strong></code>
<div class="block">
 Specifies the rule for the pattern that defines a recurring appointment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#INTERNET_SUBJECT">INTERNET_SUBJECT</a></strong></code>
<div class="block">
 Specifies the subject of the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#KEYWORDS">KEYWORDS</a></strong></code>
<div class="block">
 Contains keywords or categories for the Message object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LAST_AUTHOR">LAST_AUTHOR</a></strong></code>
<div class="block">
 Specifies the most recent author of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LAST_PRINTED">LAST_PRINTED</a></strong></code>
<div class="block">
 Specifies the time, in UTC, that the file was last printed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LAST_SAVE_DATE_TIME">LAST_SAVE_DATE_TIME</a></strong></code>
<div class="block">
 Specifies the time, in UTC, that the file was last saved.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LINE_COUNT">LINE_COUNT</a></strong></code>
<div class="block">
 Specifies the number of lines in the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LINKS_DIRTY">LINKS_DIRTY</a></strong></code>
<div class="block">
 Indicates whether the links in the document are up-to-date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#LOCATION_URL">LOCATION_URL</a></strong></code>
<div class="block">
 Area: Calendar
 Canonical name: PidNameLocationUrl
 Alternate names: urn:schemas:calendar:locationurl, LocationUrl</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MANAGER">MANAGER</a></strong></code>
<div class="block">
 Specifies the manager of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#MULTIMEDIA_CLIP_COUNT">MULTIMEDIA_CLIP_COUNT</a></strong></code>
<div class="block">
 Specifies the number of multimedia clips in the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#NOTE_COUNT">NOTE_COUNT</a></strong></code>
<div class="block">
 Specifies the number of notes in the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OMS_ACCOUNT_GUID">OMS_ACCOUNT_GUID</a></strong></code>
<div class="block">
 Contains the GUID of the SMS account used to deliver the message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OMS_MOBILE_MODEL">OMS_MOBILE_MODEL</a></strong></code>
<div class="block">
 Indicates the model of the mobile device used to send the SMS or MMS message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OMS_SCHEDULE_TIME">OMS_SCHEDULE_TIME</a></strong></code>
<div class="block">
 Contains the time, in UTC, at which the client requested that the service provider send the SMS or MMS message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OMS_SERVICE_TYPE">OMS_SERVICE_TYPE</a></strong></code>
<div class="block">
 Contains the type of service used to send an SMS or MMS message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#OMS_SOURCE_TYPE">OMS_SOURCE_TYPE</a></strong></code>
<div class="block">
 Contains the source of an SMS or MMS message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PAGE_COUNT">PAGE_COUNT</a></strong></code>
<div class="block">
 Specifies the page count of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PARAGRAPH_COUNT">PARAGRAPH_COUNT</a></strong></code>
<div class="block">
 Specifies the number of paragraphs in the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PHISHING_STAMP">PHISHING_STAMP</a></strong></code>
<div class="block">
 Indicates whether a message is likely to be phishing.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#PRESENTATION_FORMAT">PRESENTATION_FORMAT</a></strong></code>
<div class="block">
 Specifies the presentation format of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#QUARANTINE_ORIGINAL_SENDER">QUARANTINE_ORIGINAL_SENDER</a></strong></code>
<div class="block">
 Specifies the original sender of a message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#REVISION_NUMBER">REVISION_NUMBER</a></strong></code>
<div class="block">
 Specifies the revision number of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#RIGHTS_MANAGEMENT_LICENSE">RIGHTS_MANAGEMENT_LICENSE</a></strong></code>
<div class="block">
 Specifies the value used to cache the Use License for the rights-managed email message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SCALE">SCALE</a></strong></code>
<div class="block">
 Indicates whether the image is to be scaled or cropped.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SECURITY">SECURITY</a></strong></code>
<div class="block">
 Specifies the security level of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SLIDE_COUNT">SLIDE_COUNT</a></strong></code>
<div class="block">
 Specifies the number of slides in the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#SUBJECT">SUBJECT</a></strong></code>
<div class="block">
 Specifies the subject of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TEMPLATE">TEMPLATE</a></strong></code>
<div class="block">
 Specifies the template of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#THUMBNAIL">THUMBNAIL</a></strong></code>
<div class="block">
 Specifies the data representing the thumbnail image of the document.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#TITLE">TITLE</a></strong></code>
<div class="block">
 Specifies the title of the file attached to the Document object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#WORD_COUNT">WORD_COUNT</a></strong></code>
<div class="block">
 Specifies the word count of the file attached to the Document object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_CALL_ID">X_CALL_ID</a></strong></code>
<div class="block">
 Contains a unique identifier associated with the phone call.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_FAX_NUMBER_OF_PAGES">X_FAX_NUMBER_OF_PAGES</a></strong></code>
<div class="block">
 Specifies how many discrete pages are contained within an attachment representing a facsimile message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_REQUIRE_PROTECTED_PLAY_ON_PHONE">X_REQUIRE_PROTECTED_PLAY_ON_PHONE</a></strong></code>
<div class="block">
 Indicates that the client only renders the message on a phone.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SENDER_TELEPHONE_NUMBER">X_SENDER_TELEPHONE_NUMBER</a></strong></code>
<div class="block">
 Contains the telephone number of the caller associated with a voice mail message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_BROWSE_URL">X_SHARING_BROWSE_URL</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_CAPABILITIES">X_SHARING_CAPABILITIES</a></strong></code>
<div class="block">
 Contains a string representation of the value of the PidLidSharingCapabilities property (section 2.237).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_CONFIG_URL">X_SHARING_CONFIG_URL</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingConfigurationUrl property (section 2.238).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_EXENDED_CAPS">X_SHARING_EXENDED_CAPS</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_FLAVOR">X_SHARING_FLAVOR</a></strong></code>
<div class="block">
 Contains a hexadecimal string representation of the value of the PidLidSharingFlavor property (section 2.245).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_INSTANCE_GUID">X_SHARING_INSTANCE_GUID</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_LOCAL_TYPE">X_SHARING_LOCAL_TYPE</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingLocalType property (section 2.259).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_PROVIDER_GUID">X_SHARING_PROVIDER_GUID</a></strong></code>
<div class="block">
 Contains the hexadecimal string representation of the value of the PidLidSharingProviderGuid property (section 2.266).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_PROVIDER_NAME">X_SHARING_PROVIDER_NAME</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingProviderName property (section 2.267).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_PROVIDER_URL">X_SHARING_PROVIDER_URL</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingProviderUrl property (section 2.268).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_REMOTE_NAME">X_SHARING_REMOTE_NAME</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingRemoteName property (section 2.277).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_REMOTE_PATH">X_SHARING_REMOTE_PATH</a></strong></code>
<div class="block">
 Contains a value that is ignored by the server no matter what value is generated by the client.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_REMOTE_STORE_UID">X_SHARING_REMOTE_STORE_UID</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingRemoteStoreUid property (section 2.282).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_REMOTE_TYPE">X_SHARING_REMOTE_TYPE</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingRemoteType property (section 2.281).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_SHARING_REMOTE_UID">X_SHARING_REMOTE_UID</a></strong></code>
<div class="block">
 Contains the same value as the PidLidSharingRemoteUid property (section 2.282).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_VOICE_MESSAGE_ATTACHMENT_ORDER">X_VOICE_MESSAGE_ATTACHMENT_ORDER</a></strong></code>
<div class="block">
 Contains the list of names for the audio file attachments that are to be played as part of a message, in reverse order.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_VOICE_MESSAGE_DURATION">X_VOICE_MESSAGE_DURATION</a></strong></code>
<div class="block">
 Specifies the length of the attached audio message, in seconds.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#X_VOICE_MESSAGE_SENDER_NAME">X_VOICE_MESSAGE_SENDER_NAME</a></strong></code>
<div class="block">
 Contains the name of the caller who left the attached voice message, as provided by the voice network's caller ID system.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(java.lang.String,%20int,%20java.util.UUID)">find</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
    int&nbsp;type,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Finds <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email"><code>PidNamePropertyDescriptor</code></a> property in list according to required parameters</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">KnownPropertyList.</span><code><strong><a href="../../../../com/aspose/email/KnownPropertyList.html#find(java.lang.String,%20java.util.UUID)">find</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
    <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Finds <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email"><code>PidNamePropertyDescriptor</code></a> property in list according to required parameters
 This is simplified search operation without data type comparison.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></code></td>
<td class="colLast"><span class="strong">PropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PropertyDescriptor.html#getInstance(java.lang.String,%20int,%20java.util.UUID)">getInstance</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
           int&nbsp;dataType,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/UUID.html?is-external=true" title="class or interface in java.util">UUID</a>&nbsp;propertySet)</code>
<div class="block">
 Retrieves <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email"><code>PidNamePropertyDescriptor</code></a> object</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidNamePropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html#op_Equality(com.aspose.email.PidNamePropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Equality</a></strong>(<a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a>&nbsp;pd1,
           <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are equal to each another.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">PidNamePropertyDescriptor.</span><code><strong><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html#op_Inequality(com.aspose.email.PidNamePropertyDescriptor,%20com.aspose.email.PropertyDescriptor)">op_Inequality</a></strong>(<a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a>&nbsp;pd1,
             <a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&nbsp;pd2)</code>
<div class="block">
 Determines whether the specified objects are not equal to each another.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MapiNamedProperty.html#MapiNamedProperty(com.aspose.email.INamedPropertyTagProvider,%20com.aspose.email.PidNamePropertyDescriptor,%20java.lang.Object)">MapiNamedProperty</a></strong>(<a href="../../../../com/aspose/email/INamedPropertyTagProvider.html" title="interface in com.aspose.email">INamedPropertyTagProvider</a>&nbsp;tagProvider,
                 <a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">PidNamePropertyDescriptor</a>&nbsp;pd,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;data)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MapiNamedProperty.html" title="class in com.aspose.email"><code>MapiNamedProperty</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PidNamePropertyDescriptor.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PidNamePropertyDescriptor.html" target="_top">Frames</a></li>
<li><a href="PidNamePropertyDescriptor.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
