<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.HeaderType (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.HeaderType (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/HeaderType.html" target="_top">Frames</a></li>
<li><a href="HeaderType.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.HeaderType" class="title">Uses of Class<br>com.aspose.email.HeaderType</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getApparentlyTo()">getApparentlyTo</a></strong>()</code>
<div class="block">
 Inserted by sending e-mail when there is no 'To:' recipient in the original message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getApprovedBy()">getApprovedBy</a></strong>()</code>
<div class="block">
 Name of the moderator of the mailing list to which this message is sent; necessary on a posting sent to a moderated mailing list to allow its distribution to the list members.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 A copy of the e-mail message that is sent to one or more recipients without the knowledge of the primary recipients.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getCC()">getCC</a></strong>()</code>
<div class="block">
 This header can be considered an extension of the 'To:' field as it is used to specifiy additional recipients.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getComments()">getComments</a></strong>()</code>
<div class="block">
 This is a free-form header field defined in RFC2822.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getContentTransferEncoding()">getContentTransferEncoding</a></strong>()</code>
<div class="block">
 The third of the MIME-related headers.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getContentType()">getContentType</a></strong>()</code>
<div class="block">
  The 'Content-Type' defines the format of content (character set etc.) Note that the values for this header are defined in different ways in RFC1049 and in MIME (RFC2045).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getDate()">getDate</a></strong>()</code>
<div class="block">
 This header specifies a date (and time), normally the date the message was composed and sent.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getDispositionNotificationTo()">getDispositionNotificationTo</a></strong>()</code>
<div class="block">
 When the DispositionNotificationTo field is set, a request for a MDN (Message Delivery Notification) is made.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getFollowupTo()">getFollowupTo</a></strong>()</code>
<div class="block">
 Used in Usenet News to indicate that future discussions (=follow-up) on an article should go to a different set of newsgroups than the replied-to article.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getFrom()">getFrom</a></strong>()</code>
<div class="block">
 This field contains the identity of the person(s) who wished this message to be sent.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getImportance()">getImportance</a></strong>()</code>
<div class="block">
 Gets the importance.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getInReplyTo()">getInReplyTo</a></strong>()</code>
<div class="block">
 Reference to message which this message is a reply to.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getMessageID()">getMessageID</a></strong>()</code>
<div class="block">
 Unique ID of this message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getMIMEVersion()">getMIMEVersion</a></strong>()</code>
<div class="block">
 An indicator that this message is formatted according to the MIME standard, and an indication of which version of MIME is utilized.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getNewsgroups()">getNewsgroups</a></strong>()</code>
<div class="block">
 In Usenet News: group(s) to which this article was posted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getReceived()">getReceived</a></strong>()</code>
<div class="block">
 Trace of MTAs which a message has passed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getReferences()">getReferences</a></strong>()</code>
<div class="block">
 Reference to other related messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getReplyTo()">getReplyTo</a></strong>()</code>
<div class="block">
 This header field is meant to indicate where the sender wants replies to go.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getReturnPath()">getReturnPath</a></strong>()</code>
<div class="block">
 Used to convey the information from the MAIL FROM envelope attribute in final delivery, when the message leaves the SMTP environment in which 'MAIL FROM' is used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getReturnReceiptTo()">getReturnReceiptTo</a></strong>()</code>
<div class="block">
 A sender can request a return-receipt by including this header field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getSender()">getSender</a></strong>()</code>
<div class="block">
 The person or agent submitting the message to the network, if other than shown by the From: header field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getSensitivity()">getSensitivity</a></strong>()</code>
<div class="block">
 Gets the sensitivity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getSubject()">getSubject</a></strong>()</code>
<div class="block">
 Title, heading, subject.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Primary recipients.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getXConfirmReadingTo()">getXConfirmReadingTo</a></strong>()</code>
<div class="block">
 This header requests an automated confirmation notice when the message is received or read.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#getXMailer()">getXMailer</a></strong>()</code>
<div class="block">
 Information about the client software of the originator.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">HeaderType.</span><code><strong><a href="../../../../com/aspose/email/HeaderType.html#toString(com.aspose.email.HeaderType)">toString</a></strong>(<a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">HeaderType</a>&nbsp;type)</code>
<div class="block">
 Performs an implicit conversion from <a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email"><code>HeaderType</code></a> to <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/HeaderType.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/HeaderType.html" target="_top">Frames</a></li>
<li><a href="HeaderType.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
