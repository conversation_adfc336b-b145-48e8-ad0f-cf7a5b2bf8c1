<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.PersonalStorage (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.PersonalStorage (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PersonalStorage.html" target="_top">Frames</a></li>
<li><a href="PersonalStorage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.PersonalStorage" class="title">Uses of Class<br>com.aspose.email.PersonalStorage</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(java.io.OutputStream,%20int)">create</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;version)</code>
<div class="block">
 Creates the PST in a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(java.io.OutputStream,%20int,%20boolean)">create</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;version,
      boolean&nbsp;leaveStreamOpen)</code>
<div class="block">
 Creates the PST in a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(java.io.OutputStream,%20int,%20int)">create</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;stream,
      int&nbsp;blockSize,
      int&nbsp;version)</code>
<div class="block">
 Creates the PST in a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(java.nio.channels.SeekableByteChannel,%20int)">create</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/nio/channels/SeekableByteChannel.html?is-external=true" title="class or interface in java.nio.channels">SeekableByteChannel</a>&nbsp;channel,
      int&nbsp;version)</code>
<div class="block">
 Creates the PST in a java.nio.channels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(com.aspose.ms.System.IO.Stream,%20int)">create</a></strong>(Stream&nbsp;stream,
      int&nbsp;version)</code>
<div class="block">
 Creates the PST in a stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(com.aspose.ms.System.IO.Stream,%20int,%20boolean)">create</a></strong>(Stream&nbsp;stream,
      int&nbsp;version,
      boolean&nbsp;leaveStreamOpen)</code>
<div class="block">
 Creates the PST in a stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#create(java.lang.String,%20int)">create</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
      int&nbsp;version)</code>
<div class="block">
 Creates the new PST file with the specified file name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromFile(java.lang.String)">fromFile</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">
 Load PST from file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromFile(java.lang.String,%20boolean)">fromFile</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
        boolean&nbsp;writable)</code>
<div class="block">
 Load PST from file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromFile(java.lang.String,%20com.aspose.email.PersonalStorageLoadOptions)">fromFile</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
        <a href="../../../../com/aspose/email/PersonalStorageLoadOptions.html" title="class in com.aspose.email">PersonalStorageLoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 Load PST from file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(java.io.InputStream)">fromStream</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(java.io.InputStream,%20boolean)">fromStream</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
          boolean&nbsp;writable)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(java.io.InputStream,%20com.aspose.email.PersonalStorageLoadOptions)">fromStream</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
          <a href="../../../../com/aspose/email/PersonalStorageLoadOptions.html" title="class in com.aspose.email">PersonalStorageLoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(com.aspose.ms.System.IO.Stream)">fromStream</a></strong>(Stream&nbsp;stream)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(com.aspose.ms.System.IO.Stream,%20boolean)">fromStream</a></strong>(Stream&nbsp;stream,
          boolean&nbsp;writable)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#fromStream(com.aspose.ms.System.IO.Stream,%20com.aspose.email.PersonalStorageLoadOptions)">fromStream</a></strong>(Stream&nbsp;stream,
          <a href="../../../../com/aspose/email/PersonalStorageLoadOptions.html" title="class in com.aspose.email">PersonalStorageLoadOptions</a>&nbsp;loadOptions)</code>
<div class="block">
 Load PST from stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">StorageProcessedEventArgs.</span><code><strong><a href="../../../../com/aspose/email/StorageProcessedEventArgs.html#getChunk()">getChunk</a></strong>()</code>
<div class="block">
 Gets the pst that represents the chunk.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.io.InputStream,%20java.io.OutputStream)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;mboxrdDataStream,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;pstDataStream)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.io.InputStream,%20java.io.OutputStream,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;mboxrdDataStream,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.io.InputStream,%20java.io.OutputStream,%20com.aspose.email.MboxToPstConversionOptions)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;mboxrdDataStream,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MboxToPstConversionOptions.html" title="class in com.aspose.email">MboxToPstConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.io.InputStream,%20java.lang.String)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;mboxrdDataStream,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFileName)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.io.InputStream,%20java.lang.String,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;mboxrdDataStream,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFileName,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(com.aspose.ms.System.IO.Stream,%20com.aspose.ms.System.IO.Stream)">mboxToPst</a></strong>(Stream&nbsp;mboxrdDataStream,
         Stream&nbsp;pstDataStream)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(com.aspose.ms.System.IO.Stream,%20com.aspose.ms.System.IO.Stream,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(Stream&nbsp;mboxrdDataStream,
         Stream&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(com.aspose.ms.System.IO.Stream,%20com.aspose.ms.System.IO.Stream,%20com.aspose.email.MboxToPstConversionOptions)">mboxToPst</a></strong>(Stream&nbsp;mboxrdDataStream,
         Stream&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MboxToPstConversionOptions.html" title="class in com.aspose.email">MboxToPstConversionOptions</a>&nbsp;options)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20java.io.OutputStream)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;pstDataStream)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20java.io.OutputStream,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/io/OutputStream.html?is-external=true" title="class or interface in java.io">OutputStream</a>&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20com.aspose.ms.System.IO.Stream)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         Stream&nbsp;pstDataStream)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20com.aspose.ms.System.IO.Stream,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         Stream&nbsp;pstDataStream,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20java.lang.String)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFileName)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20java.lang.String,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFileName,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(java.lang.String,%20java.lang.String,%20com.aspose.email.MboxToPstConversionOptions)">mboxToPst</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mboxFileName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFileName,
         <a href="../../../../com/aspose/email/MboxToPstConversionOptions.html" title="class in com.aspose.email">MboxToPstConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.IConnection,%20com.aspose.email.PersonalStorage,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapFolderInfoCollection,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapFolderInfoCollection.html" title="class in com.aspose.email">ImapFolderInfoCollection</a>&nbsp;folders,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapRestoreSettingsAsync)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            <a href="../../../../com/aspose/email/ImapRestoreSettingsAsync.html" title="class in com.aspose.email">ImapRestoreSettingsAsync</a>&nbsp;settings)</code>
<div class="block">
 Begins to restore imap folders from the given personal storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20int)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20int,%20com.aspose.ms.System.AsyncCallback)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options,
            AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginRestore(com.aspose.email.PersonalStorage,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginRestore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
            int&nbsp;options,
            AsyncCallback&nbsp;callback,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginRestore(PersonalStorage pst, RestoreSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">MailboxConverter.</span><code><strong><a href="../../../../com/aspose/email/MailboxConverter.html#convertPersonalStorageToMbox(com.aspose.email.PersonalStorage,%20com.aspose.email.MboxStorageWriter,%20com.aspose.email.MessageAcceptanceCallback)">convertPersonalStorageToMbox</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;personalStorage,
                            <a href="../../../../com/aspose/email/MboxStorageWriter.html" title="class in com.aspose.email">MboxStorageWriter</a>&nbsp;mboxStorageWriter,
                            <a href="../../../../com/aspose/email/MessageAcceptanceCallback.html" title="class in com.aspose.email">MessageAcceptanceCallback</a>&nbsp;acceptanceCallback)</code>
<div class="block">
 Converts the <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email"><code>PersonalStorage</code></a> to mbox format using given <a href="../../../../com/aspose/email/MboxStorageWriter.html" title="class in com.aspose.email"><code>MboxStorageWriter</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">MailboxConverter.</span><code><strong><a href="../../../../com/aspose/email/MailboxConverter.html#convertPersonalStorageToMbox(com.aspose.email.PersonalStorage,%20java.lang.String,%20com.aspose.email.MessageAcceptanceCallback)">convertPersonalStorageToMbox</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;personalStorage,
                            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;storagePath,
                            <a href="../../../../com/aspose/email/MessageAcceptanceCallback.html" title="class in com.aspose.email">MessageAcceptanceCallback</a>&nbsp;acceptanceCallback)</code>
<div class="block">
 Converts the <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email"><code>PersonalStorage</code></a> to mbox format using given path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(com.aspose.email.MboxStorageReader,%20com.aspose.email.PersonalStorage,%20java.lang.String,%20com.aspose.email.MailStorageConverter.MailHandler)">mboxToPst</a></strong>(<a href="../../../../com/aspose/email/MboxStorageReader.html" title="class in com.aspose.email">MboxStorageReader</a>&nbsp;mboxStorageReader,
         <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFolderName,
         <a href="../../../../com/aspose/email/MailStorageConverter.MailHandler.html" title="class in com.aspose.email">MailStorageConverter.MailHandler</a>&nbsp;mailHandler)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="strong">MailStorageConverter.</span><code><strong><a href="../../../../com/aspose/email/MailStorageConverter.html#mboxToPst(com.aspose.email.MboxStorageReader,%20com.aspose.email.PersonalStorage,%20java.lang.String,%20com.aspose.email.MboxToPstConversionOptions)">mboxToPst</a></strong>(<a href="../../../../com/aspose/email/MboxStorageReader.html" title="class in com.aspose.email">MboxStorageReader</a>&nbsp;mboxStorageReader,
         <a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;pstFolderName,
         <a href="../../../../com/aspose/email/MboxToPstConversionOptions.html" title="class in com.aspose.email">MboxToPstConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Converts an Mbox storage to PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restore(com.aspose.email.PersonalStorage,%20com.aspose.email.ExchangeFolderInfoCollection,%20int)">restore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
       <a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a>&nbsp;folders,
       int&nbsp;options)</code>
<div class="block">
 Restores the specified exchange folders from the given personal storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#restore(com.aspose.email.PersonalStorage,%20com.aspose.email.ImapRestoreSettings)">restore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
       <a href="../../../../com/aspose/email/ImapRestoreSettings.html" title="class in com.aspose.email">ImapRestoreSettings</a>&nbsp;settings)</code>
<div class="block">
 Begins to restore imap folders from the given personal storage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#restore(com.aspose.email.PersonalStorage,%20int)">restore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
       int&nbsp;options)</code>
<div class="block">
 Restores exchange folders from the given personal storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#restore(com.aspose.email.PersonalStorage,%20com.aspose.email.RestoreSettings)">restore</a></strong>(<a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">PersonalStorage</a>&nbsp;pst,
       <a href="../../../../com/aspose/email/RestoreSettings.html" title="class in com.aspose.email">RestoreSettings</a>&nbsp;settings)</code>
<div class="block">
 Restores the specified exchange folders from the given personal storage.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PersonalStorage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PersonalStorage.html" target="_top">Frames</a></li>
<li><a href="PersonalStorage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
