<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.ExchangeFolderInfo (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.ExchangeFolderInfo (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ExchangeFolderInfo.html" target="_top">Frames</a></li>
<li><a href="ExchangeFolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.ExchangeFolderInfo" class="title">Uses of Class<br>com.aspose.email.ExchangeFolderInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Creates new folder in the root folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String,%20int)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            int&nbsp;folderType)</code>
<div class="block">
 Creates new folder in the root folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#createFolder(java.lang.String,%20java.lang.String)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Creates the new folder with the specified name in the specified parent folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String,%20java.lang.String)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Creates the new folder with the specified name in the specified parent folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String,%20java.lang.String,%20com.aspose.email.ExchangeFolderPermissionCollection)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="../../../../com/aspose/email/ExchangeFolderPermissionCollection.html" title="class in com.aspose.email">ExchangeFolderPermissionCollection</a>&nbsp;permissions)</code>
<div class="block">
 Creates the new folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String,%20java.lang.String,%20com.aspose.email.ExchangeFolderPermissionCollection,%20java.lang.String)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="../../../../com/aspose/email/ExchangeFolderPermissionCollection.html" title="class in com.aspose.email">ExchangeFolderPermissionCollection</a>&nbsp;permissions,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderClass)</code>
<div class="block">
 Creates the new folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createFolder(java.lang.String,%20java.lang.String,%20int)">createFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            int&nbsp;folderType)</code>
<div class="block">
 Creates the new folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createPublicFolder(java.lang.String,%20com.aspose.email.ExchangeFolderPermissionCollection)">createPublicFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                  <a href="../../../../com/aspose/email/ExchangeFolderPermissionCollection.html" title="class in com.aspose.email">ExchangeFolderPermissionCollection</a>&nbsp;permissions)</code>
<div class="block">
 Creates the specified public folder in the root public folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createPublicFolder(java.lang.String,%20com.aspose.email.ExchangeFolderPermissionCollection,%20int)">createPublicFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                  <a href="../../../../com/aspose/email/ExchangeFolderPermissionCollection.html" title="class in com.aspose.email">ExchangeFolderPermissionCollection</a>&nbsp;permissions,
                  int&nbsp;folderType)</code>
<div class="block">
 Creates the specified public folder in the root public folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createPublicFolder(java.lang.String,%20java.lang.String,%20com.aspose.email.ExchangeFolderPermissionCollection)">createPublicFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                  <a href="../../../../com/aspose/email/ExchangeFolderPermissionCollection.html" title="class in com.aspose.email">ExchangeFolderPermissionCollection</a>&nbsp;permissions)</code>
<div class="block">
 Creates the specified public folder in the root public folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#getFolderInfo(java.lang.String)">getFolderInfo</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri)</code>
<div class="block">
 Gets the folder information.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#getFolderInfo(java.lang.String)">getFolderInfo</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder)</code>
<div class="block">
 Gets the folder information</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#folderExists(java.lang.String,%20java.lang.String,%20com.aspose.email.ExchangeFolderInfo[])">folderExists</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            <a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>[]&nbsp;folder)</code>
<div class="block">
 Checks whether the specified folder exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#folderExists(java.lang.String,%20java.lang.String,%20com.aspose.email.ExchangeFolderInfo[])">folderExists</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            <a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>[]&nbsp;folder)</code>
<div class="block">
 Checks whether the specified folder exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesFromPublicFolder(com.aspose.email.ExchangeFolderInfo)">listMessagesFromPublicFolder</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>&nbsp;folder)</code>
<div class="block">
 Get collection of messages from public folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#listSubFolders(com.aspose.email.ExchangeFolderInfo)">listSubFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets collection of child public folders from parent</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderInfoCollection.html" title="class in com.aspose.email">ExchangeFolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listSubFolders(com.aspose.email.ExchangeFolderInfo)">listSubFolders</a></strong>(<a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">ExchangeFolderInfo</a>&nbsp;parentFolder)</code>
<div class="block">
 Gets collection of child public folders from parent</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/ExchangeFolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/ExchangeFolderInfo.html" target="_top">Frames</a></li>
<li><a href="ExchangeFolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
