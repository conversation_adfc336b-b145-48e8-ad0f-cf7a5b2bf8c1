<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.PageInfo (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.PageInfo (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PageInfo.html" target="_top">Frames</a></li>
<li><a href="PageInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.PageInfo" class="title">Uses of Class<br>com.aspose.email.PageInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/AppointmentPageInfo.html" title="class in com.aspose.email">AppointmentPageInfo</a></strong></code>
<div class="block">
 Contains information about retrieved page when paging methods are used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ExchangeFolderPageInfo.html" title="class in com.aspose.email">ExchangeFolderPageInfo</a></strong></code>
<div class="block">
 Contains information about retrieved page when paging methods are used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html" title="class in com.aspose.email">ExchangeMessagePageInfo</a></strong></code>
<div class="block">
 Contains information about retrieved page when paging methods are used.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/GraphMessagePageInfo.html" title="class in com.aspose.email">GraphMessagePageInfo</a></strong></code>
<div class="block">
 Contains information about retrieved page when paging methods are used.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></strong></code>
<div class="block">
 Contains information about retrieved page when paging methods are used.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">PageInfo.</span><code><strong><a href="../../../../com/aspose/email/PageInfo.html#next">next</a></strong></code></td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">AppointmentPageInfo.</span><code><strong><a href="../../../../com/aspose/email/AppointmentPageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">ExchangeFolderPageInfo.</span><code><strong><a href="../../../../com/aspose/email/ExchangeFolderPageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">ExchangeMessagePageInfo.</span><code><strong><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">GraphMessagePageInfo.</span><code><strong><a href="../../../../com/aspose/email/GraphMessagePageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapPageInfo.</span><code><strong><a href="../../../../com/aspose/email/ImapPageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></code></td>
<td class="colLast"><span class="strong">PageInfo.</span><code><strong><a href="../../../../com/aspose/email/PageInfo.html#getNextPage()">getNextPage</a></strong>()</code>
<div class="block">
 Information of the next page or null if current page is last</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.email.PageSettingsAsync)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       <a href="../../../../com/aspose/email/PageSettingsAsync.html" title="class in com.aspose.email">PageSettingsAsync</a>&nbsp;settings)</code>
<div class="block">
 Begins getting the list of messages asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(int itemsPerPage, int pageOffset, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/GraphMessagePageInfo.html" title="class in com.aspose.email">GraphMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#listMessages(java.lang.String,%20com.aspose.email.PageInfo,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
            <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 List GraphMessageInfo from the parent folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.IConnection,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(int itemsPerPage, int pageOffset, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.email.PageSettings)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                  <a href="../../../../com/aspose/email/PageSettings.html" title="class in com.aspose.email">PageSettings</a>&nbsp;settings)</code>
<div class="block">
 Gets the list of messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(int itemsPerPage, int pageOffset, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.PageInfo,%20com.aspose.email.PageSettings)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                  <a href="../../../../com/aspose/email/PageSettings.html" title="class in com.aspose.email">PageSettings</a>&nbsp;settings)</code>
<div class="block">
 Gets the list of messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html" title="class in com.aspose.email">ExchangeMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html" title="class in com.aspose.email">ExchangeMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.PageInfo,%20int)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                  int&nbsp;options)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeFolderPageInfo.html" title="class in com.aspose.email">ExchangeFolderPageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listSubFoldersByPage(java.lang.String,%20com.aspose.email.PageInfo)">listSubFoldersByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;parentFolderUri,
                    <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page)</code>
<div class="block">
 Searches the specified folder in the given parent folder with paging
 Method supports paging.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/PageInfo.html" target="_top">Frames</a></li>
<li><a href="PageInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
