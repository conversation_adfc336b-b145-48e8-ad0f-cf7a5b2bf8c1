<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MailQuery (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MailQuery (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailQuery.html" target="_top">Frames</a></li>
<li><a href="MailQuery.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MailQuery" class="title">Uses of Class<br>com.aspose.email.MailQuery</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxMailQuery.html" title="class in com.aspose.email">ExchangeAdvancedSyntaxMailQuery</a></strong></code>
<div class="block">
 Represents the search criteria, that are used to match several message properties in the mailbox.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#before(java.util.Date)">before</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that the date in message must be earlier than the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#before(java.util.Date,%20int)">before</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
      int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that the date in message must be earlier than the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#beforeOrEqual(java.util.Date)">beforeOrEqual</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that the date in message must be earlier or equel to the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#beforeOrEqual(java.util.Date,%20int)">beforeOrEqual</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
             int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that the date in message must be earlier or equel to the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#contains(java.lang.String)">contains</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field in message must contain the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#contains(java.lang.String,%20boolean)">contains</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
        boolean&nbsp;ignoreCase)</code>
<div class="block">
 Indicates that field in message must contain the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ComparisonField.</span><code><strong><a href="../../../../com/aspose/email/ComparisonField.html#createKey(java.lang.String,%20java.lang.Enum,%20java.lang.String)">createKey</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang">Enum</a>&nbsp;value,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;comparsionOperator)</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ComparisonField.</span><code><strong><a href="../../../../com/aspose/email/ComparisonField.html#createKey(java.lang.String,%20java.lang.String,%20java.lang.String)">createKey</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;comparsionOperator)</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#createKey(java.lang.String,%20java.lang.String,%20java.lang.String)">createKey</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;comparsionOperator)</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ESearchOptions.</span><code><strong><a href="../../../../com/aspose/email/ESearchOptions.html#createQuery()">createQuery</a></strong>()</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ModificationSequenceField.</span><code><strong><a href="../../../../com/aspose/email/ModificationSequenceField.html#createQuery()">createQuery</a></strong>()</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">SequenceSetField.</span><code><strong><a href="../../../../com/aspose/email/SequenceSetField.html#createQuery()">createQuery</a></strong>()</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ESearchOptions.</span><code><strong><a href="../../../../com/aspose/email/ESearchOptions.html#createQuery(com.aspose.email.MailQueryBuilder)">createQuery</a></strong>(<a href="../../../../com/aspose/email/MailQueryBuilder.html" title="class in com.aspose.email">MailQueryBuilder</a>&nbsp;owner)</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#customSearch(java.lang.String)">customSearch</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldValue)</code>
<div class="block">
 Search messages according to extended server search syntax.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#empty()">empty</a></strong>()</code>
<div class="block">
 Indicates that field in message must be empty.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BoolComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BoolComparisonField.html#equals(boolean)">equals</a></strong>(boolean&nbsp;value)</code>
<div class="block">
 Indicates that field must be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BinaryComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BinaryComparisonField.html#equals(byte[])">equals</a></strong>(byte[]&nbsp;value)</code>
<div class="block">
 Indicates that field must be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#equals(double)">equals</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">EnumComparisonField.</span><code><strong><a href="../../../../com/aspose/email/EnumComparisonField.html#equals(int)">equals</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field in message must be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#equals(int)">equals</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#equals(long)">equals</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BinaryComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BinaryComparisonField.html#equals(java.lang.String)">equals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field must be equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#equals(java.lang.String)">equals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field in message must be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#equals(java.lang.String,%20boolean)">equals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
      boolean&nbsp;ignoreCase)</code>
<div class="block">
 Indicates that field in message must be equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ComparisonFieldAdapter.</span><code><strong><a href="../../../../com/aspose/email/ComparisonFieldAdapter.html#exists(boolean)">exists</a></strong>(boolean&nbsp;value)</code>
<div class="block">
 Get a search expression 
 that returns true if the supplied property exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#findConversationThread(com.aspose.email.MessageInfo)">findConversationThread</a></strong>(<a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email">MessageInfo</a>&nbsp;relatedMessage)</code>
<div class="block">
 Finds the conversation thread.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#getQuery()">getQuery</a></strong>()</code>
<div class="block">
 Gets the query.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getQuery()">getQuery</a></strong>()</code>
<div class="block">
 Gets the query.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#greater(java.util.Date)">greater</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that date in message must be later than the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#greater(java.util.Date,%20int)">greater</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
       int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that date in message must be later than the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#greater(double)">greater</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater than the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#greater(int)">greater</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater than the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#greater(long)">greater</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater than the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#greaterOrEqual(double)">greaterOrEqual</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater or equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#greaterOrEqual(int)">greaterOrEqual</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater or equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#greaterOrEqual(long)">greaterOrEqual</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must be greater or equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#hasFlags(com.aspose.email.ImapMessageFlags)">hasFlags</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Search messages with the specified flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ExchangeQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeQueryBuilder.html#hasFlags(int)">hasFlags</a></strong>(int&nbsp;flags)</code>
<div class="block">
 Search messages with the specified flags.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#hasFlags(long)">hasFlags</a></strong>(long&nbsp;flags)</code>
<div class="block">
 Search messages with the specified flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#hasHeader(java.lang.String,%20java.lang.String)">hasHeader</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldName,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldValue)</code>
<div class="block">
 Search messages that have a header with the specified field-name
 and that contains the specified string
 in the text of the header (what comes after the colon).</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ImapQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ImapQueryBuilder.html#hasNoFlags(com.aspose.email.ImapMessageFlags)">hasNoFlags</a></strong>(<a href="../../../../com/aspose/email/ImapMessageFlags.html" title="class in com.aspose.email">ImapMessageFlags</a>&nbsp;flags)</code>
<div class="block">
 Search messages with the unspecified flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ExchangeQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeQueryBuilder.html#hasNoFlags(int)">hasNoFlags</a></strong>(int&nbsp;flags)</code>
<div class="block">
 Search messages with the unspecified flags.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#hasNoFlags(long)">hasNoFlags</a></strong>(long&nbsp;flags)</code>
<div class="block">
 Search messages with the unspecified flags.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#hasNoSubfolders()">hasNoSubfolders</a></strong>()</code>
<div class="block">
 Search folders which does not contains subfolders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#hasSubfolders()">hasSubfolders</a></strong>()</code>
<div class="block">
 Search folders which contains subfolders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">EnumComparisonField.</span><code><strong><a href="../../../../com/aspose/email/EnumComparisonField.html#in(java.lang.Iterable)">in</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;values)</code>
<div class="block">
 Indicates that field vlaue in message must be in list of specified values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#less(double)">less</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must be less than the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#less(int)">less</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must be less than the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#less(long)">less</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must be less than the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#lessOrEqual(double)">lessOrEqual</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must be less or equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#lessOrEqual(int)">lessOrEqual</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must be less or equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#lessOrEqual(long)">lessOrEqual</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must be less or equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#notContains(java.lang.String)">notContains</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field in message must not contain the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#notContains(java.lang.String,%20boolean)">notContains</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
           boolean&nbsp;ignoreCase)</code>
<div class="block">
 Indicates that field in message must not contain the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#notEmpty()">notEmpty</a></strong>()</code>
<div class="block">
 Indicates that field in message must not be empty.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BoolComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BoolComparisonField.html#notEquals(boolean)">notEquals</a></strong>(boolean&nbsp;value)</code>
<div class="block">
 Indicates that field must not be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BinaryComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BinaryComparisonField.html#notEquals(byte[])">notEquals</a></strong>(byte[]&nbsp;value)</code>
<div class="block">
 Indicates that field must not be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DoubleComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DoubleComparisonField.html#notEquals(double)">notEquals</a></strong>(double&nbsp;value)</code>
<div class="block">
 Indicates that field must not be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">EnumComparisonField.</span><code><strong><a href="../../../../com/aspose/email/EnumComparisonField.html#notEquals(int)">notEquals</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field in message must not be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">IntComparisonField.</span><code><strong><a href="../../../../com/aspose/email/IntComparisonField.html#notEquals(int)">notEquals</a></strong>(int&nbsp;value)</code>
<div class="block">
 Indicates that field must not be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">LongComparisonField.</span><code><strong><a href="../../../../com/aspose/email/LongComparisonField.html#notEquals(long)">notEquals</a></strong>(long&nbsp;value)</code>
<div class="block">
 Indicates that field must not be 
 equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">BinaryComparisonField.</span><code><strong><a href="../../../../com/aspose/email/BinaryComparisonField.html#notEquals(java.lang.String)">notEquals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field must not be equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#notEquals(java.lang.String)">notEquals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Indicates that field in message must not be equal to the specified value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#notEquals(java.lang.String,%20boolean)">notEquals</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value,
         boolean&nbsp;ignoreCase)</code>
<div class="block">
 Indicates that field in message must not be equal to the specified value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">EnumComparisonField.</span><code><strong><a href="../../../../com/aspose/email/EnumComparisonField.html#notIn(java.lang.Iterable)">notIn</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;values)</code>
<div class="block">
 Indicates that field vlaue in message must not be in list of specified values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#notOn(java.util.Date)">notOn</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that the date in message must not be in the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#notOn(java.util.Date,%20int)">notOn</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
     int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that the date in message must not be in the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#on(java.util.Date)">on</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that the date in message must be within the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#on(java.util.Date,%20int)">on</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
  int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that the date in message must be within the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#or(com.aspose.email.MailQuery,%20com.aspose.email.MailQuery)">or</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query1,
  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query2)</code>
<div class="block">
 Search messages that match either search key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#or(com.aspose.email.MailQuery,%20com.aspose.email.MailQuery)">or</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query1,
  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query2)</code>
<div class="block">
 Search messages that match either search key.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#since(java.util.Date)">since</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value)</code>
<div class="block">
 Indicates that date in message must be within or later than the specified date.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">DateComparisonField.</span><code><strong><a href="../../../../com/aspose/email/DateComparisonField.html#since(java.util.Date,%20int)">since</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;value,
     int&nbsp;comparisonType)</code>
<div class="block">
 Indicates that date in message must be within or later than the specified date.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">SequenceSetField.</span><code><strong><a href="../../../../com/aspose/email/SequenceSetField.html#to_MailQuery(com.aspose.email.SequenceSetField)">to_MailQuery</a></strong>(<a href="../../../../com/aspose/email/SequenceSetField.html" title="class in com.aspose.email">SequenceSetField</a>&nbsp;seqSetField)</code>
<div class="block">
 Creates the search key.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.IConnection,%20int,%20boolean,%20com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 int&nbsp;fields,
                 boolean&nbsp;closeTransaction,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
  Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int,%20java.lang.Iterable,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;messageExtraFields,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(com.aspose.email.MailQuery)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.MailQuery)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
  Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#beginListMessages(com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins ListMessage operation asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(com.aspose.email.MailQuery,%20int)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(java.lang.String,%20com.aspose.email.MailQuery)">beginListMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins getting the list of messages asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(java.lang.String,%20com.aspose.email.MailQuery,%20int)">beginListMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback)">beginListMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages,
                 AsyncCallback&nbsp;callback)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessages(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                 int&nbsp;maxNumberOfMessages,
                 AsyncCallback&nbsp;callback,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block">
 Begins getting the list of messages asynchronously in the current folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.email.PageSettingsAsync)">beginListMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       <a href="../../../../com/aspose/email/PageSettingsAsync.html" title="class in com.aspose.email">PageSettingsAsync</a>&nbsp;settings)</code>
<div class="block">
 Begins getting the list of messages asynchronously</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       int&nbsp;itemsPerPage,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#beginListMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginListMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                       <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                       <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                       AsyncCallback&nbsp;callback,
                       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use BeginListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettingsAsync settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MboxMessageInfo.html" title="class in com.aspose.email">MboxMessageInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessageInfo(com.aspose.email.MailQuery)">enumerateMessageInfo</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Enumerates the message information that matches the specified query.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.EmlLoadOptions,%20com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/EmlLoadOptions.html" title="class in com.aspose.email">EmlLoadOptions</a>&nbsp;options,
                 <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Enumerates the mail messages that match the specified query, using the provided load options.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email">MessageInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#enumerateMessages(com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;mailQuery)</code>
<div class="block">
 Retrieves a collection of <a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email"><code>MessageInfo</code></a> objects that match the specified query.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">MboxStorageReader.</span><code><strong><a href="../../../../com/aspose/email/MboxStorageReader.html#enumerateMessages(com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Enumerates the mail messages that match the specified query.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/OlmMessageInfo.html" title="class in com.aspose.email">OlmMessageInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">OlmFolder.</span><code><strong><a href="../../../../com/aspose/email/OlmFolder.html#enumerateMessages(com.aspose.email.MailQuery)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">MailQuery.</span><code><strong><a href="../../../../com/aspose/email/MailQuery.html#equals(com.aspose.email.MailQuery)">equals</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;other)</code>
<div class="block">
 Indicates whether the current object is equal to another object of the same type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Contact.html" title="class in com.aspose.email">Contact</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#findPeople(java.lang.String,%20com.aspose.email.MailQuery,%20int)">findPeople</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
          <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
          int&nbsp;maxNumberOfItems)</code>
<div class="block">
 Find contacts located in the specified user's personal mailbox on server.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MessageInfoCollection.html" title="class in com.aspose.email">MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getContents(com.aspose.email.MailQuery)">getContents</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Get collection of messages.<br>
 Method is used to display brief message information <a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email"><code>MessageInfo</code></a> like subject, sender, recipients.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MessageInfoCollection.html" title="class in com.aspose.email">MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getContents(com.aspose.email.MailQuery,%20int,%20int)">getContents</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
           int&nbsp;startIndex,
           int&nbsp;count)</code>
<div class="block">
 Get collection of messages.<br>
 Method is used to display brief message information <a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email"><code>MessageInfo</code></a> like subject, sender, recipients.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfoCollection.html" title="class in com.aspose.email">FolderInfoCollection</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getSubFolders(com.aspose.email.MailQuery)">getSubFolders</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Gets collection of subfolders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(com.aspose.email.MailQuery)">listAppointments</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(com.aspose.email.MailQuery,%20boolean)">listAppointments</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for default calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String,%20com.aspose.email.MailQuery)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email">Appointment</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointments(java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listAppointments</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves list of appointments for specified calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppointmentPageInfo.html" title="class in com.aspose.email">AppointmentPageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointmentsByPage(com.aspose.email.MailQuery,%20int)">listAppointmentsByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                      int&nbsp;itemsPerPage)</code>
<div class="block">
 Retrieves page with appointments for calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppointmentPageInfo.html" title="class in com.aspose.email">AppointmentPageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointmentsByPage(com.aspose.email.MailQuery,%20int,%20int)">listAppointmentsByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                      int&nbsp;itemsPerPage,
                      int&nbsp;itemOffset)</code>
<div class="block">
 Retrieves page with appointments for calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppointmentPageInfo.html" title="class in com.aspose.email">AppointmentPageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointmentsByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int)">listAppointmentsByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                      <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                      int&nbsp;itemsPerPage)</code>
<div class="block">
 Retrieves page with appointments for specified calendar folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/AppointmentPageInfo.html" title="class in com.aspose.email">AppointmentPageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listAppointmentsByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20int)">listAppointmentsByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                      <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                      int&nbsp;itemsPerPage,
                      int&nbsp;itemOffset)</code>
<div class="block">
 Retrieves page with appointments for specified calendar folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listItems(java.lang.String,%20com.aspose.email.MailQuery)">listItems</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieve list of item uries in specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listItems(java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listItems</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
         boolean&nbsp;recursive)</code>
<div class="block">
 Retrieve list of item uries in specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listItems(java.lang.String,%20java.lang.String,%20com.aspose.email.MailQuery)">listItems</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieve list of item uries in specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listItems(java.lang.String,%20java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listItems</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
         boolean&nbsp;recursive)</code>
<div class="block">
 Retrieve list of item uries in specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Pop3MessageInfoCollection.html" title="class in com.aspose.email">Pop3MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#listMessages(com.aspose.email.IConnection,%20int,%20boolean,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            int&nbsp;fields,
            boolean&nbsp;closeTransaction,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Lists the messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Pop3MessageInfoCollection.html" title="class in com.aspose.email">Pop3MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#listMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Lists the messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(com.aspose.email.IConnection,%20com.aspose.email.MailQuery,%20int)">listMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int)">listMessages</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Pop3MessageInfoCollection.html" title="class in com.aspose.email">Pop3MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#listMessages(int,%20boolean,%20com.aspose.email.MailQuery)">listMessages</a></strong>(int&nbsp;fields,
            boolean&nbsp;closeTransaction,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Lists the messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/Pop3MessageInfoCollection.html" title="class in com.aspose.email">Pop3MessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">Pop3Client.</span><code><strong><a href="../../../../com/aspose/email/Pop3Client.html#listMessages(com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Lists the messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(com.aspose.email.MailQuery,%20int)">listMessages</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessages(java.lang.String,%20int,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            int&nbsp;maxNumberOfMessages,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessages(java.lang.String,%20int,%20com.aspose.email.MailQuery,%20boolean)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            int&nbsp;maxNumberOfMessages,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            boolean&nbsp;recursive)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessages(java.lang.String,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#listMessages(java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            boolean&nbsp;recursive)</code>
<div class="block">
 Lists the messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessages(java.lang.String,%20com.aspose.email.MailQuery,%20boolean)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            boolean&nbsp;recursive)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapMessageInfoCollection.html" title="class in com.aspose.email">ImapMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessages(java.lang.String,%20com.aspose.email.MailQuery,%20int)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
            int&nbsp;maxNumberOfMessages)</code>
<div class="block">
 Gets the list of messages in the current folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/GraphMessagePageInfo.html" title="class in com.aspose.email">GraphMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#listMessages(java.lang.String,%20com.aspose.email.PageInfo,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id,
            <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;page,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 List GraphMessageInfo from the parent folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessageInfoCollection.html" title="class in com.aspose.email">ExchangeMessageInfoCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessages(java.lang.String,%20java.lang.String,%20com.aspose.email.MailQuery)">listMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mailbox,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
            <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20int)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  int&nbsp;itemsPerPage)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.IConnection,%20java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/IConnection.html" title="interface in com.aspose.email">IConnection</a>&nbsp;connection,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(com.aspose.email.MailQuery,%20com.aspose.email.PageInfo,%20com.aspose.email.PageSettings)">listMessagesByPage</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo,
                  <a href="../../../../com/aspose/email/PageSettings.html" title="class in com.aspose.email">PageSettings</a>&nbsp;settings)</code>
<div class="block">
 Gets the list of messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html" title="class in com.aspose.email">ExchangeMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  int&nbsp;itemsPerPage)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  int&nbsp;itemsPerPage)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ExchangeMessagePageInfo.html" title="class in com.aspose.email">ExchangeMessagePageInfo</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20int,%20int)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  int&nbsp;itemsPerPage,
                  int&nbsp;offset)</code>
<div class="block">
 List the messages in the specified folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/ImapPageInfo.html" title="class in com.aspose.email">ImapPageInfo</a></code></td>
<td class="colLast"><span class="strong">ImapClient.</span><code><strong><a href="../../../../com/aspose/email/ImapClient.html#listMessagesByPage(java.lang.String,%20com.aspose.email.MailQuery,%20com.aspose.email.PageInfo)">listMessagesByPage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderName,
                  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
                  <a href="../../../../com/aspose/email/PageInfo.html" title="class in com.aspose.email">PageInfo</a>&nbsp;pageInfo)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ListMessagesByPage(MailQuery query, PageInfo pageInfo, PageSettings settings) method instead of it.</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/TaskCollection.html" title="class in com.aspose.email">TaskCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listTasks(java.lang.String,%20int,%20com.aspose.email.MailQuery)">listTasks</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         int&nbsp;maxNumberOfItems,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves lists of exchange tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/TaskCollection.html" title="class in com.aspose.email">TaskCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listTasks(java.lang.String,%20int,%20com.aspose.email.MailQuery,%20boolean)">listTasks</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         int&nbsp;maxNumberOfItems,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query,
         boolean&nbsp;recursive)</code>
<div class="block">
 Retrieves lists of exchange tasks.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/TaskCollection.html" title="class in com.aspose.email">TaskCollection</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#listTasks(java.lang.String,%20com.aspose.email.MailQuery)">listTasks</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
         <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query)</code>
<div class="block">
 Retrieves lists of exchange tasks.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#or(com.aspose.email.MailQuery,%20com.aspose.email.MailQuery)">or</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query1,
  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query2)</code>
<div class="block">
 Search messages that match either search key.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#or(com.aspose.email.MailQuery,%20com.aspose.email.MailQuery)">or</a></strong>(<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query1,
  <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&nbsp;query2)</code>
<div class="block">
 Search messages that match either search key.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#splitInto(com.aspose.ms.System.Collections.Generic.IGenericList,%20java.lang.String)">splitInto</a></strong>(IGenericList&lt;<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&gt;&nbsp;criteria,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;path)</code>
<div class="block">
 Splits the pst storage based on criteria.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#splitInto(com.aspose.ms.System.Collections.Generic.IGenericList,%20java.lang.String,%20java.lang.String)">splitInto</a></strong>(IGenericList&lt;<a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">MailQuery</a>&gt;&nbsp;criteria,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;partFileNamePrefix,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;path)</code>
<div class="block">
 Splits the pst storage based on criteria.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MailQuery.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MailQuery.html" target="_top">Frames</a></li>
<li><a href="MailQuery.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
