<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.MapiMessage (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.MapiMessage (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiMessage.html" target="_top">Frames</a></li>
<li><a href="MapiMessage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.MapiMessage" class="title">Uses of Class<br>com.aspose.email.MapiMessage</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MapiMessage,%20com.aspose.email.SmimeKey)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a copy of the specified MapiMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MapiMessage,%20com.aspose.email.SmimeKey,%20com.aspose.email.SignatureOptions)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key,
               <a href="../../../../com/aspose/email/SignatureOptions.html" title="class in com.aspose.email">SignatureOptions</a>&nbsp;options)</code>
<div class="block">
 Creates a copy of the specified MapiMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ForwardMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ForwardMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ReplyMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ReplyMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the replying messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding and replying messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#copyMessage(java.lang.String,%20java.lang.String)">copyMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;newParentId,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;itemId)</code>
<div class="block">
 Copy a Message to another mailfolder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#createCalendarSharingInvitationMessage(java.lang.String)">createCalendarSharingInvitationMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;recipient)</code>
<div class="block">
 Create calendar sharing invitation message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createMessage(java.lang.String,%20com.aspose.email.MapiMessage)">createMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderId,
             <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Creates message in specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#decrypt(byte[],%20java.lang.String)">decrypt</a></strong>(byte[]&nbsp;certificateRawData,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;certificatePassword)</code>
<div class="block">
 Decrypts this message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#decrypt(com.aspose.email.SmimeKey)">decrypt</a></strong>(<a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Decrypts this message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#deepClone()">deepClone</a></strong>()</code>
<div class="block">
 Creates a new object that is a copy of the current instance.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">OlmStorage.</span><code><strong><a href="../../../../com/aspose/email/OlmStorage.html#extractMapiMessage(com.aspose.email.OlmMessageInfo)">extractMapiMessage</a></strong>(<a href="../../../../com/aspose/email/OlmMessageInfo.html" title="class in com.aspose.email">OlmMessageInfo</a>&nbsp;messageInfo)</code>
<div class="block">
 Get the message from OLM storage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">OlmStorage.</span><code><strong><a href="../../../../com/aspose/email/OlmStorage.html#extractMapiMessage(java.lang.String)">extractMapiMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">Get the message from OLM.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#extractMessage(byte[])">extractMessage</a></strong>(byte[]&nbsp;entryId)</code>
<div class="block">
 Get the message from PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#extractMessage(com.aspose.email.MessageInfo)">extractMessage</a></strong>(<a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email">MessageInfo</a>&nbsp;messageInfo)</code>
<div class="block">
 Get the message from PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#extractMessage(java.lang.String)">extractMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryId)</code>
<div class="block">
 Get the message from PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchItem(java.lang.String)">fetchItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri)</code>
<div class="block">
 Retrieves the complete item with attachments.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchItem(java.lang.String,%20java.lang.Iterable)">fetchItem</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
         <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Retrieves the complete item with attachments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ExchangeClient.</span><code><strong><a href="../../../../com/aspose/email/ExchangeClient.html#fetchMapiMessage(java.lang.String)">fetchMapiMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;messageUri)</code>
<div class="block">
 Fetches the mapi message with specified uri.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessage(java.lang.String)">fetchMapiMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessage(java.lang.String,%20java.lang.Iterable)">fetchMapiMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uri,
                <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>This method is obsolete and will be removed soon. Please use FetchItem method</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessages(java.lang.Iterable)">fetchMapiMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uris)</code>
<div class="block">
 Fetches the speciifed messages</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#fetchMapiMessages(java.lang.Iterable,%20java.lang.Iterable)">fetchMapiMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;uris,
                 <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/PropertyDescriptor.html" title="class in com.aspose.email">PropertyDescriptor</a>&gt;&nbsp;extendedProperties)</code>
<div class="block">
 Fetches the speciifed messages</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#fetchMessage(java.lang.String)">fetchMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;id)</code>
<div class="block">
 Gets message in specified id</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(java.io.InputStream)">fromMailMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Creates an instance of MapiMessage from the EML format data stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(com.aspose.email.MailMessage)">fromMailMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message)</code>
<div class="block">
 Creates an instance of MapiMessage from the MailMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(com.aspose.email.MailMessage,%20com.aspose.email.MapiConversionOptions)">fromMailMessage</a></strong>(<a href="../../../../com/aspose/email/MailMessage.html" title="class in com.aspose.email">MailMessage</a>&nbsp;message,
               <a href="../../../../com/aspose/email/MapiConversionOptions.html" title="class in com.aspose.email">MapiConversionOptions</a>&nbsp;options)</code>
<div class="block">
 Creates an instance of MapiMessage from the MailMessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromMailMessage(java.lang.String)">fromMailMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">
 Creates an instance of MapiMessage from the MailMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#fromProperties(com.aspose.email.MapiPropertyCollection)">fromProperties</a></strong>(<a href="../../../../com/aspose/email/MapiPropertyCollection.html" title="class in com.aspose.email">MapiPropertyCollection</a>&nbsp;properties)</code>
<div class="block">
 Creates an instance of MapiMessage from a collection of Mapi properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">RestoredItemEntry.</span><code><strong><a href="../../../../com/aspose/email/RestoredItemEntry.html#getItem()">getItem</a></strong>()</code>
<div class="block">
 Gets the soft-deleted message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MessageAddedEventArgs.</span><code><strong><a href="../../../../com/aspose/email/MessageAddedEventArgs.html#getMessage()">getMessage</a></strong>()</code>
<div class="block">
 Gets the message that has been added.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiCalendar.</span><code><strong><a href="../../../../com/aspose/email/MapiCalendar.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">
 Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiContact.</span><code><strong><a href="../../../../com/aspose/email/MapiContact.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiDistributionList.</span><code><strong><a href="../../../../com/aspose/email/MapiDistributionList.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiJournal.</span><code><strong><a href="../../../../com/aspose/email/MapiJournal.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiNote.</span><code><strong><a href="../../../../com/aspose/email/MapiNote.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiTask.</span><code><strong><a href="../../../../com/aspose/email/MapiTask.html#getUnderlyingMessage()">getUnderlyingMessage</a></strong>()</code>
<div class="block">Retrieves the underlying MapiMessage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#load(java.io.InputStream)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Loads message from stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#load(java.io.InputStream,%20com.aspose.email.LoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream,
    <a href="../../../../com/aspose/email/LoadOptions.html" title="class in com.aspose.email">LoadOptions</a>&nbsp;options)</code>
<div class="block">
 Loads message from stream with additional options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#load(java.lang.String)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">
 Loads message from file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#load(java.lang.String,%20com.aspose.email.LoadOptions)">load</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
    <a href="../../../../com/aspose/email/LoadOptions.html" title="class in com.aspose.email">LoadOptions</a>&nbsp;options)</code>
<div class="block">
 Loads message from file with additional options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#loadFromTnef(java.io.InputStream)">loadFromTnef</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;stream)</code>
<div class="block">
 Loads message from Transport Neutral Encapsulation Format (TNEF) data structure</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#loadFromTnef(java.lang.String)">loadFromTnef</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">
 Loads message from Transport Neutral Encapsulation Format (TNEF) data structure</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#moveMessage(java.lang.String,%20java.lang.String)">moveMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;newParentId,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;itemId)</code>
<div class="block">
 Move a message to another mailfolder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessageReader.</span><code><strong><a href="../../../../com/aspose/email/MapiMessageReader.html#readMessage()">readMessage</a></strong>()</code>
<div class="block">
 Parse the current stream and returns the data as a MapiMessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiMessage.</span><code><strong><a href="../../../../com/aspose/email/MapiMessage.html#removeSignature()">removeSignature</a></strong>()</code>
<div class="block">
 Remove signature.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#toMapiMessage()">toMapiMessage</a></strong>()</code>
<div class="block">
 Converts ICalendar item (.ics) to MAPI (.msg) message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">MapiObjectProperty.</span><code><strong><a href="../../../../com/aspose/email/MapiObjectProperty.html#toMapiMessage()">toMapiMessage</a></strong>()</code>
<div class="block">
 Creates the MapiMessage from object data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MapiMessage)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MapiMessage,%20com.aspose.email.UpdateSettings)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message,
             <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#enumerateMapiMessages()">enumerateMapiMessages</a></strong>()</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">OlmFolder.</span><code><strong><a href="../../../../com/aspose/email/OlmFolder.html#enumerateMapiMessages()">enumerateMapiMessages</a></strong>()</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&gt;</code></td>
<td class="colLast"><span class="strong">OlmStorage.</span><code><strong><a href="../../../../com/aspose/email/OlmStorage.html#enumerateMessages(com.aspose.email.OlmFolder)">enumerateMessages</a></strong>(<a href="../../../../com/aspose/email/OlmFolder.html" title="class in com.aspose.email">OlmFolder</a>&nbsp;folder)</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of messages in folder.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiAttachmentCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachmentCollection.html#add(java.lang.String,%20com.aspose.email.MapiMessage)">add</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
   <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Adds the new attachment as embedded message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addMessage(com.aspose.email.MapiMessage)">addMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Adds a new message into folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessage(com.aspose.email.MapiMessage)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;mapiMessage)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessage(com.aspose.email.MapiMessage,%20boolean)">appendMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;mapiMessage,
             boolean&nbsp;markAsSent)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMessage(java.lang.String,%20com.aspose.email.MapiMessage,%20boolean)">appendMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folder,
             <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;mapiMessage,
             boolean&nbsp;markAsSent)</code>
<div class="block">
 Uploads the mail message to the specified folder</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MapiMessage,%20com.aspose.email.SmimeKey)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Creates a copy of the specified MapiMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#attachSignature(com.aspose.email.MapiMessage,%20com.aspose.email.SmimeKey,%20com.aspose.email.SignatureOptions)">attachSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg,
               <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key,
               <a href="../../../../com/aspose/email/SignatureOptions.html" title="class in com.aspose.email">SignatureOptions</a>&nbsp;options)</code>
<div class="block">
 Creates a copy of the specified MapiMessage and adds a digital signature to it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IAsyncResult</code></td>
<td class="colLast"><span class="strong">MessageAcceptanceCallback.</span><code><strong><a href="../../../../com/aspose/email/MessageAcceptanceCallback.html#beginInvoke(com.aspose.email.MapiMessage,%20com.aspose.ms.System.AsyncCallback,%20java.lang.Object)">beginInvoke</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;mapiMessage,
           AsyncCallback&nbsp;callback,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;state)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ForwardMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ForwardMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ReplyMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ReplyMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the replying messages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">ResponseMessageBuilder.</span><code><strong><a href="../../../../com/aspose/email/ResponseMessageBuilder.html#buildResponse(com.aspose.email.MapiMessage)">buildResponse</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Builds the forwarding and replying messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/SmimeResult.html" title="class in com.aspose.email">SmimeResult</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#checkSignature(com.aspose.email.MapiMessage)">checkSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Checking signature MapiMessage.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/SmimeResult.html" title="class in com.aspose.email">SmimeResult</a></code></td>
<td class="colLast"><span class="strong">SecureEmailManager.</span><code><strong><a href="../../../../com/aspose/email/SecureEmailManager.html#checkSignature(com.aspose.email.MapiMessage,%20com.aspose.email.SmimeKey)">checkSignature</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg,
              <a href="../../../../com/aspose/email/SmimeKey.html" title="class in com.aspose.email">SmimeKey</a>&nbsp;key)</code>
<div class="block">
 Checking signature MapiMessage.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#createMessage(java.lang.String,%20com.aspose.email.MapiMessage)">createMessage</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderId,
             <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Creates message in specified folder</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiAttachmentCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachmentCollection.html#insert(int,%20java.lang.String,%20com.aspose.email.MapiMessage)">insert</a></strong>(int&nbsp;index,
      <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
      <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Inserts a message as attachment into the <a href="../../../../com/aspose/email/MapiAttachmentCollection.html" title="class in com.aspose.email"><code>MapiAttachmentCollection</code></a> at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="strong">MessageAcceptanceCallback.</span><code><strong><a href="../../../../com/aspose/email/MessageAcceptanceCallback.html#invoke(com.aspose.email.MapiMessage)">invoke</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;mapiMessage)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">MapiAttachmentCollection.</span><code><strong><a href="../../../../com/aspose/email/MapiAttachmentCollection.html#replace(int,%20java.lang.String,%20com.aspose.email.MapiMessage)">replace</a></strong>(int&nbsp;index,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
       <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;msg)</code>
<div class="block">
 Replaces an element at the specified index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#send(com.aspose.email.MapiMessage)">send</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email message</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#send(com.aspose.email.MapiMessage,%20boolean)">send</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message,
    boolean&nbsp;saveToSentItems)</code>
<div class="block">
 Sends email message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#sendAsMime(com.aspose.email.MapiMessage)">sendAsMime</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Sends email message using MIME format</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MapiMessage)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></code></td>
<td class="colLast"><span class="strong">IGraphClient.</span><code><strong><a href="../../../../com/aspose/email/IGraphClient.html#updateMessage(com.aspose.email.MapiMessage,%20com.aspose.email.UpdateSettings)">updateMessage</a></strong>(<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message,
             <a href="../../../../com/aspose/email/UpdateSettings.html" title="class in com.aspose.email">UpdateSettings</a>&nbsp;updateSettings)</code>
<div class="block">
 Updates message</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with type arguments of type <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addMessages(java.lang.Iterable)">addMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Provides message adding in a bulk mode.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>[]</code></td>
<td class="colLast"><span class="strong">IEWSClient.</span><code><strong><a href="../../../../com/aspose/email/IEWSClient.html#appendMapiMessages(java.lang.String,%20java.lang.Iterable)">appendMapiMessages</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;folderUri,
                  <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>&lt;<a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&gt;&nbsp;messages)</code>
<div class="block">
 Uploads the mapi messages to the specified folder</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MessageAddedEventArgs.html#MessageAddedEventArgs(java.lang.String,%20com.aspose.email.MapiMessage)">MessageAddedEventArgs</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryId,
                     <a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">MapiMessage</a>&nbsp;message)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/MessageAddedEventArgs.html" title="class in com.aspose.email"><code>MessageAddedEventArgs</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/MapiMessage.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/MapiMessage.html" target="_top">Frames</a></li>
<li><a href="MapiMessage.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
