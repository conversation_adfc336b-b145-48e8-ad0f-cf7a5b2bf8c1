<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:45 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.RecurrencePattern (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.RecurrencePattern (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/RecurrencePattern.html" target="_top">Frames</a></li>
<li><a href="RecurrencePattern.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.RecurrencePattern" class="title">Uses of Class<br>com.aspose.email.RecurrencePattern</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/DailyRecurrencePattern.html" title="class in com.aspose.email">DailyRecurrencePattern</a></strong></code>
<div class="block">
 Represents a recurrence pattern of daily recurrence type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/MonthlyRecurrencePattern.html" title="class in com.aspose.email">MonthlyRecurrencePattern</a></strong></code>
<div class="block">
 Represents a recurrence pattern of monthly recurrence type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/TaskRegeneratingPattern.html" title="class in com.aspose.email">TaskRegeneratingPattern</a></strong></code>
<div class="block">
 Represents the regenerating recurrence pattern that specifies how many days, weeks, months or years 
 after the completion of the current task the next occurrence will be due.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/WeeklyRecurrencePattern.html" title="class in com.aspose.email">WeeklyRecurrencePattern</a></strong></code>
<div class="block">
 Represents a recurrence pattern of weekly recurrence type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/YearlyRecurrencePattern.html" title="class in com.aspose.email">YearlyRecurrencePattern</a></strong></code>
<div class="block">
 Represents a recurrence pattern of yearly recurrence type.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#getRecurrence()">getRecurrence</a></strong>()</code>
<div class="block">
 Gets or sets the recurrence pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></code></td>
<td class="colLast"><span class="strong">ExchangeTask.</span><code><strong><a href="../../../../com/aspose/email/ExchangeTask.html#getRecurrencePattern()">getRecurrencePattern</a></strong>()</code>
<div class="block">
 Gets or sets a recurrence information for a recurring task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></code></td>
<td class="colLast"><span class="strong">RecurrencePattern.</span><code><strong><a href="../../../../com/aspose/email/RecurrencePattern.html#to_RecurrencePattern(java.lang.String)">to_RecurrencePattern</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">
 Converts string representation of recurrence pattern in ICalendar format to object</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Appointment.</span><code><strong><a href="../../../../com/aspose/email/Appointment.html#setRecurrence(com.aspose.email.RecurrencePattern)">setRecurrence</a></strong>(<a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;value)</code>
<div class="block">
 Gets or sets the recurrence pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">ExchangeTask.</span><code><strong><a href="../../../../com/aspose/email/ExchangeTask.html#setRecurrencePattern(com.aspose.email.RecurrencePattern)">setRecurrencePattern</a></strong>(<a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;value)</code>
<div class="block">
 Gets or sets a recurrence information for a recurring task.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/Appointment.html#Appointment(java.lang.String,%20java.lang.String,%20java.lang.String,%20java.util.Date,%20java.util.Date,%20com.aspose.email.MailAddress,%20com.aspose.email.MailAddressCollection,%20java.lang.String,%20com.aspose.email.RecurrencePattern)">Appointment</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;location,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;summary,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;description,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;startDate,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/util/Date.html?is-external=true" title="class or interface in java.util">Date</a>&nbsp;endDate,
           <a href="../../../../com/aspose/email/MailAddress.html" title="class in com.aspose.email">MailAddress</a>&nbsp;organizer,
           <a href="../../../../com/aspose/email/MailAddressCollection.html" title="class in com.aspose.email">MailAddressCollection</a>&nbsp;attendees,
           <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;uid,
           <a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">RecurrencePattern</a>&nbsp;recurrencePattern)</code>
<div class="block">
 Initialize a new instance of the <a href="../../../../com/aspose/email/Appointment.html" title="class in com.aspose.email"><code>Appointment</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/RecurrencePattern.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/RecurrencePattern.html" target="_top">Frames</a></li>
<li><a href="RecurrencePattern.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
