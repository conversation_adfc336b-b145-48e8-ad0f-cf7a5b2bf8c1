<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:45 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.StringComparisonField (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.StringComparisonField (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/StringComparisonField.html" target="_top">Frames</a></li>
<li><a href="StringComparisonField.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.StringComparisonField" class="title">Uses of Class<br>com.aspose.email.StringComparisonField</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ComparisonFieldAdapter.</span><code><strong><a href="../../../../com/aspose/email/ComparisonFieldAdapter.html#getAsString()">getAsString</a></strong>()</code>
<div class="block">
 Returns current <a href="../../../../com/aspose/email/ComparisonField.html" title="class in com.aspose.email"><code>ComparisonField</code></a> as <a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email"><code>StringComparisonField</code></a></div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#getAttachment()">getAttachment</a></strong>()</code>
<div class="block">
 Gets the field that allows to find items with a specified attachment name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getBcc()">getBcc</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's BCC field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getBody()">getBody</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the body of the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#getCategory()">getCategory</a></strong>()</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getCc()">getCc</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's CC field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ContactQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ContactQueryBuilder.html#getContactDisplayName()">getContactDisplayName</a></strong>()</code>
<div class="block"><strong>Deprecated.</strong>&nbsp;
<div class="block"><i>Please, use ExchangeQueryBuilder.getContact().getDisplayName()</i></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#getContainerClass()">getContainerClass</a></strong>()</code>
<div class="block">
 Gets folders with an specified message class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeQueryBuilder.html#getContentClass()">getContentClass</a></strong>()</code>
<div class="block">
 Gets items with an specified content class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ContactQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ContactQueryBuilder.html#getDisplayName()">getDisplayName</a></strong>()</code>
<div class="block">
 Gets the field that allows to find Contact that contain the specified string in the DisplayName field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">AppointmentQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/AppointmentQueryBuilder.html#getEndTimeZoneId()">getEndTimeZoneId</a></strong>()</code>
<div class="block">
 Gets the field that allows to find items with a specified EndTimeZoneId.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#getFolderName()">getFolderName</a></strong>()</code>
<div class="block">
 Search folders with an specified display name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getFrom()">getFrom</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's FROM field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#getImportance()">getImportance</a></strong>()</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">AppointmentQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/AppointmentQueryBuilder.html#getLocation()">getLocation</a></strong>()</code>
<div class="block">
 Gets the field that allows to find items with a specified Location.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#getMessageClass()">getMessageClass</a></strong>()</code>
<div class="block">
 Gets messages with an specified message class.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeQueryBuilder.html#getMessageId()">getMessageId</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's MessageId field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">PersonalStorageQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorageQueryBuilder.html#getMessageId()">getMessageId</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's MessageId field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">ExchangeAdvancedSyntaxQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/ExchangeAdvancedSyntaxQueryBuilder.html#getParticipants()">getParticipants</a></strong>()</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">AppointmentQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/AppointmentQueryBuilder.html#getStartTimeZoneId()">getStartTimeZoneId</a></strong>()</code>
<div class="block">
 Gets the field that allows to find items with a specified StartTimeZoneId.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getSubject()">getSubject</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's SUBJECT field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getText()">getText</a></strong>()</code>
<div class="block">
 Gets the field that allows to find the messages that contain the specified string in the headers (subject, from, to, cc) and body of the message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">MailQueryBuilder.</span><code><strong><a href="../../../../com/aspose/email/MailQueryBuilder.html#getTo()">getTo</a></strong>()</code>
<div class="block">
 Gets the field that allows to find messages that contain the specified string in the envelope structure's TO field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">StringComparisonField</a></code></td>
<td class="colLast"><span class="strong">StringComparisonField.</span><code><strong><a href="../../../../com/aspose/email/StringComparisonField.html#setKQL(boolean)">setKQL</a></strong>(boolean&nbsp;isKQL)</code>
<div class="block">
 Gets or sets value which defines whether property is used for Keyword Query Language</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/StringComparisonField.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/StringComparisonField.html" target="_top">Frames</a></li>
<li><a href="StringComparisonField.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
