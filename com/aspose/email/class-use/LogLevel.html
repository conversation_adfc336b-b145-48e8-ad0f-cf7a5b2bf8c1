<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:43 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.LogLevel (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.LogLevel (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/LogLevel.html" target="_top">Frames</a></li>
<li><a href="LogLevel.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.LogLevel" class="title">Uses of Class<br>com.aspose.email.LogLevel</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> declared as <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Debug">Debug</a></strong></code>
<div class="block">
 The Debug level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Error">Error</a></strong></code>
<div class="block">
 The Error level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Fatal">Fatal</a></strong></code>
<div class="block">
 The Fatal level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Information">Information</a></strong></code>
<div class="block">
 The Info level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Trace">Trace</a></strong></code>
<div class="block">
 The Trace level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#Warning">Warning</a></strong></code>
<div class="block">
 The Warn level.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">LogEntry.</span><code><strong><a href="../../../../com/aspose/email/LogEntry.html#getSeverity()">getSeverity</a></strong>()</code>
<div class="block">
 Log entry severity as a <code>Severity</code>(<a href="../../../../com/aspose/email/LogEntry.html#getSeverity()"><code>LogEntry.getSeverity()</code></a>/<a href="../../../../com/aspose/email/LogEntry.html#setSeverity(com.aspose.email.LogLevel)"><code>LogEntry.setSeverity(LogLevel)</code></a>) enumeration.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#getSeverity()">getSeverity</a></strong>()</code>
<div class="block">
 Gets or sets severity.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Logger.html" title="class in com.aspose.email">Logger</a></code></td>
<td class="colLast"><span class="strong">LoggerManager.</span><code><strong><a href="../../../../com/aspose/email/LoggerManager.html#createLogger(java.lang.String,%20com.aspose.email.LogLevel,%20com.aspose.email.Appender...)">createLogger</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity,
            <a href="../../../../com/aspose/email/Appender.html" title="class in com.aspose.email">Appender</a>...&nbsp;appenders)</code>
<div class="block">
 Creates a logger with specified name and appender.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/aspose/email/Logger.html" title="class in com.aspose.email">Logger</a></code></td>
<td class="colLast"><span class="strong">LoggerManager.</span><code><strong><a href="../../../../com/aspose/email/LoggerManager.html#createLogger(java.lang.String,%20com.aspose.email.LogLevel,%20com.aspose.email.Appender)">createLogger</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity,
            <a href="../../../../com/aspose/email/Appender.html" title="class in com.aspose.email">Appender</a>&nbsp;appender)</code>
<div class="block">
 Creates a logger with specified name and appender.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#isEnabled(com.aspose.email.LogLevel)">isEnabled</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;level)</code>
<div class="block">
 Determines if logging is enabled for the specified level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#op_GreaterThan(com.aspose.email.LogLevel,%20com.aspose.email.LogLevel)">op_GreaterThan</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l1,
              <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l2)</code>
<div class="block">
 Compares two <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email"><code>LogLevel</code></a> objects 
 and returns a value indicating whether 
 the first one is greater than the second one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#op_GreaterThanOrEqual(com.aspose.email.LogLevel,%20com.aspose.email.LogLevel)">op_GreaterThanOrEqual</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l1,
                     <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l2)</code>
<div class="block">
 Compares two <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email"><code>LogLevel</code></a> objects 
 and returns a value indicating whether 
 the first one is greater than or equal to the second one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#op_LessThan(com.aspose.email.LogLevel,%20com.aspose.email.LogLevel)">op_LessThan</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l1,
           <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l2)</code>
<div class="block">
 Compares two <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email"><code>LogLevel</code></a> objects 
 and returns a value indicating whether 
 the first one is less than the second one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><span class="strong">LogLevel.</span><code><strong><a href="../../../../com/aspose/email/LogLevel.html#op_LessThanOrEqual(com.aspose.email.LogLevel,%20com.aspose.email.LogLevel)">op_LessThanOrEqual</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l1,
                  <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;l2)</code>
<div class="block">
 Compares two <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email"><code>LogLevel</code></a> objects 
 and returns a value indicating whether 
 the first one is less than or equal to the second one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">LogEntry.</span><code><strong><a href="../../../../com/aspose/email/LogEntry.html#setSeverity(com.aspose.email.LogLevel)">setSeverity</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;value)</code>
<div class="block">
 Log entry severity as a <code>Severity</code>(<a href="../../../../com/aspose/email/LogEntry.html#getSeverity()"><code>LogEntry.getSeverity()</code></a>/<a href="../../../../com/aspose/email/LogEntry.html#setSeverity(com.aspose.email.LogLevel)"><code>LogEntry.setSeverity(LogLevel)</code></a>) enumeration.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#setSeverity(com.aspose.email.LogLevel)">setSeverity</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;value)</code>
<div class="block">
 Gets or sets severity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#write(java.lang.String,%20java.lang.Exception,%20com.aspose.email.LogLevel)">write</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
     <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a>&nbsp;ex,
     <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;level)</code>
<div class="block">
 Writes the specified message and exception to appenders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#writeIf(com.aspose.email.LogLevel,%20java.lang.String)">writeIf</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;condition,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">
 Writes the specified message if the log level is enabled.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">Logger.</span><code><strong><a href="../../../../com/aspose/email/Logger.html#writeIf(com.aspose.email.LogLevel,%20java.lang.String,%20java.lang.Exception)">writeIf</a></strong>(<a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;condition,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
       <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a>&nbsp;exception)</code>
<div class="block">
 Writes the specified message and exception if the log level is enabled.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/LogEntry.html#LogEntry(java.lang.String,%20com.aspose.email.LogLevel)">LogEntry</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity)</code>
<div class="block">
 Initialize a new instance of a <a href="../../../../com/aspose/email/LogEntry.html" title="class in com.aspose.email"><code>LogEntry</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/LogEntry.html#LogEntry(java.lang.String,%20com.aspose.email.LogLevel,%20java.lang.String,%20int,%20java.lang.String,%20com.aspose.ms.System.Collections.Generic.IGenericDictionary)">LogEntry</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category,
        int&nbsp;eventId,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;title,
        IGenericDictionary&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;properties)</code>
<div class="block">
 Create a new instance of <a href="../../../../com/aspose/email/LogEntry.html" title="class in com.aspose.email"><code>LogEntry</code></a> with a full set of constructor parameters</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/LogEntry.html#LogEntry(java.lang.String,%20java.lang.Throwable,%20com.aspose.email.LogLevel)">LogEntry</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;innerException,
        <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity)</code>
<div class="block">
 Initialize a new instance of a <a href="../../../../com/aspose/email/LogEntry.html" title="class in com.aspose.email"><code>LogEntry</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/LogEntry.html#LogEntry(java.lang.String,%20java.lang.Throwable,%20com.aspose.email.LogLevel,%20java.lang.String,%20int,%20java.lang.String,%20com.aspose.ms.System.Collections.Generic.IGenericDictionary)">LogEntry</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/Throwable.html?is-external=true" title="class or interface in java.lang">Throwable</a>&nbsp;innerException,
        <a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">LogLevel</a>&nbsp;severity,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;category,
        int&nbsp;eventId,
        <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;title,
        IGenericDictionary&lt;<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;properties)</code>
<div class="block">
 Create a new instance of <a href="../../../../com/aspose/email/LogEntry.html" title="class in com.aspose.email"><code>LogEntry</code></a> with a full set of constructor parameters</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/LogLevel.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/LogLevel.html" target="_top">Frames</a></li>
<li><a href="LogLevel.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
