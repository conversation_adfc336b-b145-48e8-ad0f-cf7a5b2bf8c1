<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (version 1.7.0_80) on Wed Aug 06 23:09:42 CDT 2025 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class com.aspose.email.FolderInfo (Aspose.Email for Java 25.7 API Documentation)</title>
<meta name="date" content="2025-08-06">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
</head>
<body>
<script type="text/javascript"><!--
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Uses of Class com.aspose.email.FolderInfo (Aspose.Email for Java 25.7 API Documentation)";
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar_top">
<!--   -->
</a><a href="#skip-navbar_top" title="Skip navigation links"></a><a name="navbar_top_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/FolderInfo.html" target="_top">Frames</a></li>
<li><a href="FolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.aspose.email.FolderInfo" class="title">Uses of Class<br>com.aspose.email.FolderInfo</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.aspose.email">
<!--   -->
</a>
<h3>Uses of <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a> in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a></h3>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addSubFolder(java.lang.String)">addSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Adds the new sub-folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addSubFolder(java.lang.String,%20boolean)">addSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            boolean&nbsp;createHierarchy)</code>
<div class="block">
 Adds the new sub-folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addSubFolder(java.lang.String,%20com.aspose.email.FolderCreationOptions)">addSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="../../../../com/aspose/email/FolderCreationOptions.html" title="class in com.aspose.email">FolderCreationOptions</a>&nbsp;creationOptions)</code>
<div class="block">
 Adds a subfolder with the specified name to the current folder using the provided creation options.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#addSubFolder(java.lang.String,%20java.lang.String)">addSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            <a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;containerClass)</code>
<div class="block">
 Adds the new subfolder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#createPredefinedFolder(java.lang.String,%20int)">createPredefinedFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                      int&nbsp;defaultFolder)</code>
<div class="block">
 Creates the standard interpersonal message (IPM) folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#createPredefinedFolder(java.lang.String,%20int,%20boolean)">createPredefinedFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
                      int&nbsp;defaultFolder,
                      boolean&nbsp;createHierarchy)</code>
<div class="block">
 Creates the standard interpersonal message (IPM) folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">ItemMovedEventArgs.</span><code><strong><a href="../../../../com/aspose/email/ItemMovedEventArgs.html#getDestinationFolder()">getDestinationFolder</a></strong>()</code>
<div class="block">
 Gets the destination folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getFolderById(byte[])">getFolderById</a></strong>(byte[]&nbsp;entryId)</code>
<div class="block">
 Gets the personal folder from PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getFolderById(java.lang.String)">getFolderById</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryIdString)</code>
<div class="block">
 Gets the personal folder from PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getParentFolder(byte[])">getParentFolder</a></strong>(byte[]&nbsp;entryId)</code>
<div class="block">
 Gets the parent folder of message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getParentFolder(java.lang.String)">getParentFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryIdString)</code>
<div class="block">
 Gets the parent folder of message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getPredefinedFolder(int)">getPredefinedFolder</a></strong>(int&nbsp;defaultFolder)</code>
<div class="block">
 Gets the standard interpersonal message (IPM) folder from PST.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#getRootFolder()">getRootFolder</a></strong>()</code>
<div class="block">
 Gets the root folder of PST.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getSubFolder(java.lang.String)">getSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name)</code>
<div class="block">
 Get subfolder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getSubFolder(java.lang.String,%20boolean)">getSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            boolean&nbsp;ignoreCase)</code>
<div class="block">
 Gets the subfolder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#getSubFolder(java.lang.String,%20boolean,%20boolean)">getSubFolder</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name,
            boolean&nbsp;ignoreCase,
            boolean&nbsp;handlePathSeparator)</code>
<div class="block">
 Retrieves a subfolder with the specified name from the current folder.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> that return types with arguments of type <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#enumerateFolders()">enumerateFolders</a></strong>()</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of subfolders in folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>IGenericEnumerable&lt;<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&gt;</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#enumerateFolders(int)">enumerateFolders</a></strong>(int&nbsp;kind)</code>
<div class="block">
 Exposes the enumerator, which supports an iteration of subfolders in folder.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#mergeWith(com.aspose.email.FolderInfo)">mergeWith</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;sourceFolder)</code>
<div class="block">
 Merges the folder with the folder from another pst.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#mergeWith(com.aspose.email.FolderInfo,%20boolean)">mergeWith</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;sourceFolder,
         boolean&nbsp;recursiveHandler)</code>
<div class="block">
 Merges the folder with the folder from another pst.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#moveContents(com.aspose.email.FolderInfo)">moveContents</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;newFolder)</code>
<div class="block">
 Moves the contents to a new folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#moveItem(com.aspose.email.FolderInfo,%20com.aspose.email.FolderInfo)">moveItem</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;folder,
        <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;newFolder)</code>
<div class="block">
 Moves a specified folder to a new parent folder within the current pst.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#moveItem(com.aspose.email.MessageInfo,%20com.aspose.email.FolderInfo)">moveItem</a></strong>(<a href="../../../../com/aspose/email/MessageInfo.html" title="class in com.aspose.email">MessageInfo</a>&nbsp;message,
        <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;newFolder)</code>
<div class="block">
 Moves a specified message to a new folder within the current pst.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="strong">FolderInfo.</span><code><strong><a href="../../../../com/aspose/email/FolderInfo.html#moveSubfolders(com.aspose.email.FolderInfo)">moveSubfolders</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;newFolder)</code>
<div class="block">
 Moves the subfolders to a new parent folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="strong">PersonalStorage.</span><code><strong><a href="../../../../com/aspose/email/PersonalStorage.html#tryToGetFolderById(java.lang.String,%20com.aspose.email.FolderInfo[])">tryToGetFolderById</a></strong>(<a href="http://docs.oracle.com/javase/7/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entryIdString,
                  <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>[]&nbsp;folder)</code>
<div class="block">Gets the folder associated with the specified entry ID.</div>
</td>
</tr>
</tbody>
</table>
<table border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../com/aspose/email/package-summary.html">com.aspose.email</a> with parameters of type <a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><strong><a href="../../../../com/aspose/email/ItemMovedEventArgs.html#ItemMovedEventArgs(com.aspose.email.FolderInfo)">ItemMovedEventArgs</a></strong>(<a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">FolderInfo</a>&nbsp;folder)</code>
<div class="block">
 Initializes a new instance of the <a href="../../../../com/aspose/email/ItemMovedEventArgs.html" title="class in com.aspose.email"><code>ItemMovedEventArgs</code></a> class.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar_bottom">
<!--   -->
</a><a href="#skip-navbar_bottom" title="Skip navigation links"></a><a name="navbar_bottom_firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../com/aspose/email/package-summary.html">Package</a></li>
<li><a href="../../../../com/aspose/email/FolderInfo.html" title="class in com.aspose.email">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/aspose/email/class-use/FolderInfo.html" target="_top">Frames</a></li>
<li><a href="FolderInfo.html" target="_top">No Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip-navbar_bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright (c) 2008-2025 <a href="http://www.aspose.com">Aspose Pty Ltd</a>. All Rights Reserved.</small></p>
</body>
</html>
